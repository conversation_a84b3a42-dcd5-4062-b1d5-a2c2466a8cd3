// Options page script for Tech Jobs Finder
console.log('Options page loaded');

// Default settings
const defaultSettings = {
    maxJobs: 15,
    refreshInterval: 30,
    autoRefresh: true,
    notifications: true,
    enableUpwork: true,
    enableKhamsat: true,
    enableWuzzuf: true,
    preferredCategories: ['web', 'mobile'],
    keywords: '',
    excludeKeywords: '',
    enableCache: true,
    cacheExpiry: 6,
    debugMode: false
};

// DOM elements
const form = document.getElementById('settingsForm');
const statusDiv = document.getElementById('status');
const resetBtn = document.getElementById('resetBtn');
const exportBtn = document.getElementById('exportBtn');

// Initialize options page
document.addEventListener('DOMContentLoaded', loadSettings);
form.addEventListener('submit', saveSettings);
resetBtn.addEventListener('click', resetSettings);
exportBtn.addEventListener('click', exportSettings);

// Load saved settings
async function loadSettings() {
    try {
        const result = await chrome.storage.local.get(['settings']);
        const settings = result.settings || defaultSettings;
        
        // Populate form fields
        document.getElementById('maxJobs').value = settings.maxJobs;
        document.getElementById('refreshInterval').value = settings.refreshInterval;
        document.getElementById('autoRefresh').checked = settings.autoRefresh;
        document.getElementById('notifications').checked = settings.notifications;
        
        document.getElementById('enableUpwork').checked = settings.enableUpwork;
        document.getElementById('enableKhamsat').checked = settings.enableKhamsat;
        document.getElementById('enableWuzzuf').checked = settings.enableWuzzuf;
        
        // Preferred categories
        if (settings.preferredCategories) {
            settings.preferredCategories.forEach(category => {
                const checkbox = document.getElementById(`cat${category.charAt(0).toUpperCase() + category.slice(1)}`);
                if (checkbox) checkbox.checked = true;
            });
        }
        
        document.getElementById('keywords').value = settings.keywords || '';
        document.getElementById('excludeKeywords').value = settings.excludeKeywords || '';
        
        document.getElementById('enableCache').checked = settings.enableCache;
        document.getElementById('cacheExpiry').value = settings.cacheExpiry;
        document.getElementById('debugMode').checked = settings.debugMode;
        
        console.log('Settings loaded successfully');
        
    } catch (error) {
        console.error('Error loading settings:', error);
        showStatus('خطأ في تحميل الإعدادات', 'error');
    }
}

// Save settings
async function saveSettings(event) {
    event.preventDefault();
    
    try {
        // Collect preferred categories
        const preferredCategories = [];
        ['web', 'mobile', 'data', 'design'].forEach(category => {
            const checkbox = document.getElementById(`cat${category.charAt(0).toUpperCase() + category.slice(1)}`);
            if (checkbox && checkbox.checked) {
                preferredCategories.push(category);
            }
        });
        
        const settings = {
            maxJobs: parseInt(document.getElementById('maxJobs').value),
            refreshInterval: parseInt(document.getElementById('refreshInterval').value),
            autoRefresh: document.getElementById('autoRefresh').checked,
            notifications: document.getElementById('notifications').checked,
            
            enableUpwork: document.getElementById('enableUpwork').checked,
            enableKhamsat: document.getElementById('enableKhamsat').checked,
            enableWuzzuf: document.getElementById('enableWuzzuf').checked,
            
            preferredCategories: preferredCategories,
            keywords: document.getElementById('keywords').value.trim(),
            excludeKeywords: document.getElementById('excludeKeywords').value.trim(),
            
            enableCache: document.getElementById('enableCache').checked,
            cacheExpiry: parseInt(document.getElementById('cacheExpiry').value),
            debugMode: document.getElementById('debugMode').checked
        };
        
        // Save to storage
        await chrome.storage.local.set({ settings: settings });
        
        // Update auto-refresh alarm
        if (settings.autoRefresh && settings.refreshInterval > 0) {
            chrome.alarms.clear('autoRefresh');
            chrome.alarms.create('autoRefresh', {
                delayInMinutes: settings.refreshInterval,
                periodInMinutes: settings.refreshInterval
            });
        } else {
            chrome.alarms.clear('autoRefresh');
        }
        
        // Clear cache if disabled
        if (!settings.enableCache) {
            await chrome.storage.local.remove(['cachedJobs', 'lastUpdate']);
        }
        
        showStatus('تم حفظ الإعدادات بنجاح! ✅', 'success');
        console.log('Settings saved:', settings);
        
    } catch (error) {
        console.error('Error saving settings:', error);
        showStatus('خطأ في حفظ الإعدادات ❌', 'error');
    }
}

// Reset settings to default
async function resetSettings() {
    if (confirm('هل أنت متأكد من إعادة تعيين جميع الإعدادات إلى القيم الافتراضية؟')) {
        try {
            await chrome.storage.local.set({ settings: defaultSettings });
            await loadSettings();
            showStatus('تم إعادة تعيين الإعدادات بنجاح! 🔄', 'success');
            
        } catch (error) {
            console.error('Error resetting settings:', error);
            showStatus('خطأ في إعادة تعيين الإعدادات ❌', 'error');
        }
    }
}

// Export settings
async function exportSettings() {
    try {
        const result = await chrome.storage.local.get(['settings']);
        const settings = result.settings || defaultSettings;
        
        const dataStr = JSON.stringify(settings, null, 2);
        const dataBlob = new Blob([dataStr], { type: 'application/json' });
        
        const url = URL.createObjectURL(dataBlob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `tech-jobs-finder-settings-${new Date().toISOString().split('T')[0]}.json`;
        link.click();
        
        URL.revokeObjectURL(url);
        showStatus('تم تصدير الإعدادات بنجاح! 📤', 'success');
        
    } catch (error) {
        console.error('Error exporting settings:', error);
        showStatus('خطأ في تصدير الإعدادات ❌', 'error');
    }
}

// Show status message
function showStatus(message, type) {
    statusDiv.textContent = message;
    statusDiv.className = `status ${type}`;
    statusDiv.style.display = 'block';
    
    // Hide after 3 seconds
    setTimeout(() => {
        statusDiv.style.display = 'none';
    }, 3000);
}

// Add import functionality
function addImportButton() {
    const importBtn = document.createElement('button');
    importBtn.type = 'button';
    importBtn.className = 'btn btn-secondary';
    importBtn.innerHTML = '📥 استيراد الإعدادات';
    importBtn.addEventListener('click', importSettings);
    
    const buttonGroup = document.querySelector('.button-group');
    buttonGroup.appendChild(importBtn);
}

// Import settings
function importSettings() {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.json';
    input.addEventListener('change', handleImportFile);
    input.click();
}

// Handle import file
async function handleImportFile(event) {
    const file = event.target.files[0];
    if (!file) return;
    
    try {
        const text = await file.text();
        const importedSettings = JSON.parse(text);
        
        // Validate settings
        if (typeof importedSettings !== 'object') {
            throw new Error('Invalid settings format');
        }
        
        // Merge with default settings to ensure all properties exist
        const settings = { ...defaultSettings, ...importedSettings };
        
        await chrome.storage.local.set({ settings: settings });
        await loadSettings();
        showStatus('تم استيراد الإعدادات بنجاح! 📥', 'success');
        
    } catch (error) {
        console.error('Error importing settings:', error);
        showStatus('خطأ في استيراد الإعدادات - تأكد من صحة الملف ❌', 'error');
    }
}

// Add keyboard shortcuts info
function addKeyboardShortcuts() {
    const shortcutsDiv = document.createElement('div');
    shortcutsDiv.className = 'section';
    shortcutsDiv.innerHTML = `
        <h2>⌨️ اختصارات لوحة المفاتيح</h2>
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px; font-size: 14px;">
            <div><strong>Ctrl + S:</strong> حفظ الإعدادات</div>
            <div><strong>Ctrl + R:</strong> إعادة تعيين</div>
            <div><strong>Ctrl + E:</strong> تصدير الإعدادات</div>
            <div><strong>Ctrl + I:</strong> استيراد الإعدادات</div>
        </div>
    `;
    
    document.querySelector('.content').appendChild(shortcutsDiv);
}

// Add keyboard shortcuts
document.addEventListener('keydown', (event) => {
    if (event.ctrlKey) {
        switch (event.key.toLowerCase()) {
            case 's':
                event.preventDefault();
                form.dispatchEvent(new Event('submit'));
                break;
            case 'r':
                event.preventDefault();
                resetSettings();
                break;
            case 'e':
                event.preventDefault();
                exportSettings();
                break;
            case 'i':
                event.preventDefault();
                importSettings();
                break;
        }
    }
});

// Initialize additional features
document.addEventListener('DOMContentLoaded', () => {
    addImportButton();
    addKeyboardShortcuts();
});
