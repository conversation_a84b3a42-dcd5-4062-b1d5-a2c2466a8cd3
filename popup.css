* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600&display=swap');

body {
    font-family: 'Poppins', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #0A1128 0%, #1282A2 100%);
    color: #333;
    direction: rtl;
    text-align: right;
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
}

.container {
    width: 400px;
    max-height: 600px;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 24px;
    box-shadow: 0 20px 40px rgba(0,0,0,0.1),
                inset 0 2px 2px rgba(255,255,255,0.1),
                inset 0 -2px 2px rgba(0,0,0,0.1);
    overflow: hidden;
    border: 1px solid rgba(255,255,255,0.1);
}

.header {
    background: linear-gradient(135deg, rgba(18, 130, 162, 0.95) 0%, rgba(10, 17, 40, 0.95) 100%);
    color: white;
    padding: 24px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid rgba(255,255,255,0.1);
}

.header-content {
    flex: 1;
}

.header h1 {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 20px;
    font-weight: 600;
}

.header h1 i {
    font-size: 24px;
    background: linear-gradient(135deg, #FFD700, #FFA500);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
}

.header-subtitle {
    font-size: 14px;
    opacity: 0.8;
    margin-top: 4px;
}

.refresh-btn {
    background: rgba(255,255,255,0.1);
    padding: 10px 20px;
    border-radius: 16px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    border: 1px solid rgba(255,255,255,0.2);
    backdrop-filter: blur(5px);
}

.refresh-btn:hover {
    background: rgba(255,255,255,0.2);
    transform: translateY(-1px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

.refresh-btn:active {
    transform: translateY(1px);
}
    transform: translateY(-2px);
}

.filters {
    padding: 16px;
    display: flex;
    gap: 12px;
    background: rgba(255,255,255,0.05);
}

.select-wrapper {
    position: relative;
    flex: 1;
}

.select-wrapper i {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: rgba(255,255,255,0.6);
    pointer-events: none;
    z-index: 1;
}

.filters select {
    flex: 1;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    background: white;
    font-size: 12px;
    direction: rtl;
    background: rgba(255,255,255,0.1);
    border: 1px solid rgba(255,255,255,0.2);
    border-radius: 12px;
    padding: 8px 16px;
    color: white;
    font-family: inherit;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
    flex: 1;
    outline: none;
    -webkit-appearance: none;
    appearance: none;
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='white'%3e%3cpath d='M7 10l5 5 5-5z'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 8px center;
    background-size: 20px;
}

.filters select:hover {
    background-color: rgba(255,255,255,0.2);
}

.filters select option {
    background: #0A1128;
    color: white;
}

.loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px;
    color: white;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 3px solid rgba(255,255,255,0.1);
    border-radius: 50%;
    border-top-color: #1282A2;
    animation: spin 1s linear infinite;
    margin-bottom: 16px;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

.error {
    background: rgba(255,59,48,0.1);
    color: white;
    padding: 24px;
    text-align: center;
    border-radius: 16px;
    margin: 16px;
}

#retryBtn {
    background: rgba(255,255,255,0.1);
    border: 1px solid rgba(255,255,255,0.2);
    color: white;
    padding: 8px 24px;
    border-radius: 20px;
    font-family: inherit;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-top: 12px;
}

#retryBtn:hover {
    background: rgba(255,255,255,0.2);
}

.jobs-container {
    padding: 16px;
    overflow-y: auto;
    max-height: 400px;
    scrollbar-width: thin;
    scrollbar-color: rgba(255,255,255,0.2) transparent;
}

.jobs-container::-webkit-scrollbar {
    width: 6px;
}

.jobs-container::-webkit-scrollbar-track {
    background: transparent;
}

.jobs-container::-webkit-scrollbar-thumb {
    background-color: rgba(255,255,255,0.2);
    border-radius: 3px;
}

.job-card {
    background: rgba(255,255,255,0.05);
    border-radius: 16px;
    padding: 16px;
    margin-bottom: 12px;
    border: 1px solid rgba(255,255,255,0.1);
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    animation: fadeIn 0.3s ease forwards;
}

.job-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.job-card:hover::before {
    opacity: 1;
}

.job-card:nth-child(1) { animation-delay: 0.1s; }
.job-card:nth-child(2) { animation-delay: 0.2s; }
.job-card:nth-child(3) { animation-delay: 0.3s; }
.job-card:nth-child(4) { animation-delay: 0.4s; }
.job-card:nth-child(5) { animation-delay: 0.5s; }

.job-title {
    color: white;
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 8px;
}

.job-company {
    color: rgba(255,255,255,0.7);
    font-size: 14px;
    margin-bottom: 12px;
}

.job-description {
    color: #666;
    font-size: 12px;
    line-height: 1.4;
    margin-bottom: 10px;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.job-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 12px;
    color: rgba(255,255,255,0.6);
}

.job-date {
    color: #27ae60;
}

.job-price {
    font-weight: bold;
    color: #e74c3c;
}

.stats {
    padding: 12px 16px;
    background: rgba(255,255,255,0.05);
    border-top: 1px solid rgba(255,255,255,0.1);
    color: rgba(255,255,255,0.6);
    font-size: 12px;
    text-align: center;
}

.footer {
    background: #2c3e50;
    color: white;
    padding: 10px 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 10px;
}

.social-links {
    display: flex;
    gap: 10px;
}

.social-links a {
    color: white;
    text-decoration: none;
    font-size: 14px;
}
