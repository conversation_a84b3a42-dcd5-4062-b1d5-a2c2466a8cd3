* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #333;
    direction: rtl;
    text-align: right;
}

.container {
    width: 400px;
    max-height: 600px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    overflow: hidden;
}

.header {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header h1 {
    font-size: 18px;
    font-weight: bold;
}

.refresh-btn {
    background: rgba(255,255,255,0.2);
    padding: 8px 12px;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 12px;
}

.refresh-btn:hover {
    background: rgba(255,255,255,0.3);
    transform: translateY(-2px);
}

.filters {
    padding: 15px;
    background: #f8f9fa;
    display: flex;
    gap: 10px;
}

.filters select {
    flex: 1;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    background: white;
    font-size: 12px;
    direction: rtl;
}

.loading {
    text-align: center;
    padding: 40px 20px;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #4facfe;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 15px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.error {
    text-align: center;
    padding: 20px;
    color: #e74c3c;
}

.error button {
    background: #e74c3c;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 6px;
    cursor: pointer;
    margin-top: 10px;
}

.jobs-container {
    max-height: 350px;
    overflow-y: auto;
    padding: 10px;
}

.job-card {
    background: white;
    border: 1px solid #eee;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 10px;
    transition: all 0.3s ease;
    cursor: pointer;
}

.job-card:hover {
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    transform: translateY(-2px);
}

.job-title {
    font-weight: bold;
    color: #2c3e50;
    margin-bottom: 8px;
    font-size: 14px;
    line-height: 1.4;
}

.job-platform {
    display: inline-block;
    background: #4facfe;
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 10px;
    margin-bottom: 8px;
}

.job-platform.upwork { background: #14a800; }
.job-platform.khamsat { background: #ff6b35; }
.job-platform.wuzzuf { background: #1976d2; }

.job-description {
    color: #666;
    font-size: 12px;
    line-height: 1.4;
    margin-bottom: 10px;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.job-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 11px;
    color: #888;
}

.job-date {
    color: #27ae60;
}

.job-price {
    font-weight: bold;
    color: #e74c3c;
}

.stats {
    background: #f8f9fa;
    padding: 10px 15px;
    font-size: 11px;
    color: #666;
    border-top: 1px solid #eee;
}

.footer {
    background: #2c3e50;
    color: white;
    padding: 10px 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 10px;
}

.social-links {
    display: flex;
    gap: 10px;
}

.social-links a {
    color: white;
    text-decoration: none;
    font-size: 14px;
}

/* Scrollbar styling */
.jobs-container::-webkit-scrollbar {
    width: 6px;
}

.jobs-container::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.jobs-container::-webkit-scrollbar-thumb {
    background: #4facfe;
    border-radius: 3px;
}

.jobs-container::-webkit-scrollbar-thumb:hover {
    background: #357abd;
}
