// Global variables
let allJobs = [];
let filteredJobs = [];

// DOM elements
const loadingEl = document.getElementById('loading');
const errorEl = document.getElementById('error');
const jobsContainer = document.getElementById('jobsContainer');
const refreshBtn = document.getElementById('refreshBtn');
const platformFilter = document.getElementById('platformFilter');
const categoryFilter = document.getElementById('categoryFilter');
const lastUpdateEl = document.getElementById('lastUpdate');
const totalJobsEl = document.getElementById('totalJobs');

// Event listeners
document.addEventListener('DOMContentLoaded', initializeApp);
refreshBtn.addEventListener('click', loadJobs);
platformFilter.addEventListener('change', filterJobs);
categoryFilter.addEventListener('change', filterJobs);

// Initialize the application
async function initializeApp() {
    console.log('Initializing Tech Jobs Finder...');
    
    // Load cached jobs first
    await loadCachedJobs();
    
    // Then fetch fresh data
    await loadJobs();
}

// Load jobs from cache
async function loadCachedJobs() {
    try {
        const result = await chrome.storage.local.get(['cachedJobs', 'lastUpdate']);
        if (result.cachedJobs && result.cachedJobs.length > 0) {
            allJobs = result.cachedJobs;
            filteredJobs = [...allJobs];
            displayJobs();
            updateStats();
            
            if (result.lastUpdate) {
                lastUpdateEl.textContent = new Date(result.lastUpdate).toLocaleString('ar-EG');
            }
        }
    } catch (error) {
        console.error('Error loading cached jobs:', error);
    }
}

// Main function to load jobs
async function loadJobs() {
    showLoading();
    hideError();
    
    try {
        console.log('Starting to fetch jobs...');
        
        // Fetch jobs from different platforms
        const jobPromises = [
            fetchUpworkJobs(),
            fetchKhamsatJobs(),
            fetchWuzzufJobs()
        ];
        
        const results = await Promise.allSettled(jobPromises);
        
        // Combine all successful results
        allJobs = [];
        results.forEach((result, index) => {
            if (result.status === 'fulfilled' && result.value) {
                allJobs.push(...result.value);
            } else {
                console.error(`Failed to fetch from platform ${index}:`, result.reason);
            }
        });
        
        // Sort jobs by date (newest first)
        allJobs.sort((a, b) => new Date(b.date) - new Date(a.date));
        
        // Limit to 15 jobs total (5 from each platform ideally)
        allJobs = allJobs.slice(0, 15);
        
        // Cache the results
        await chrome.storage.local.set({
            cachedJobs: allJobs,
            lastUpdate: Date.now()
        });
        
        filteredJobs = [...allJobs];
        displayJobs();
        updateStats();
        hideLoading();
        
        console.log(`Successfully loaded ${allJobs.length} jobs`);
        
    } catch (error) {
        console.error('Error loading jobs:', error);
        showError();
        hideLoading();
    }
}

// Fetch Upwork jobs (mock data for now due to CORS)
async function fetchUpworkJobs() {
    console.log('Fetching Upwork jobs...');
    
    // Mock data - في التطبيق الحقيقي سنستخدم background script
    return [
        {
            id: 'upwork_1',
            title: 'Full Stack Developer - React & Node.js',
            description: 'Looking for an experienced full stack developer to build a modern web application...',
            platform: 'upwork',
            url: 'https://www.upwork.com/jobs/~01234567890',
            date: new Date().toISOString(),
            price: '$25-50/hr',
            category: 'web'
        },
        {
            id: 'upwork_2',
            title: 'Mobile App Developer - Flutter',
            description: 'Need a skilled Flutter developer to create a cross-platform mobile application...',
            platform: 'upwork',
            url: 'https://www.upwork.com/jobs/~01234567891',
            date: new Date(Date.now() - 3600000).toISOString(),
            price: '$30-60/hr',
            category: 'mobile'
        },
        {
            id: 'upwork_3',
            title: 'Data Scientist - Python & ML',
            description: 'Seeking a data scientist with expertise in Python and machine learning...',
            platform: 'upwork',
            url: 'https://www.upwork.com/jobs/~01234567892',
            date: new Date(Date.now() - 7200000).toISOString(),
            price: '$40-80/hr',
            category: 'data'
        }
    ];
}

// Fetch Khamsat jobs (mock data)
async function fetchKhamsatJobs() {
    console.log('Fetching Khamsat jobs...');
    
    return [
        {
            id: 'khamsat_1',
            title: 'تطوير موقع إلكتروني بـ WordPress',
            description: 'مطلوب مطور ووردبريس لإنشاء موقع إلكتروني احترافي للشركة...',
            platform: 'khamsat',
            url: 'https://khamsat.com/programming/web/123456',
            date: new Date(Date.now() - 1800000).toISOString(),
            price: '$200-500',
            category: 'web'
        },
        {
            id: 'khamsat_2',
            title: 'تصميم تطبيق جوال - UI/UX',
            description: 'نحتاج مصمم محترف لتصميم واجهات تطبيق جوال حديث...',
            platform: 'khamsat',
            url: 'https://khamsat.com/design/mobile/123457',
            date: new Date(Date.now() - 5400000).toISOString(),
            price: '$150-300',
            category: 'design'
        }
    ];
}

// Fetch Wuzzuf jobs (mock data)
async function fetchWuzzufJobs() {
    console.log('Fetching Wuzzuf jobs...');
    
    return [
        {
            id: 'wuzzuf_1',
            title: 'Frontend Developer - React.js',
            description: 'We are looking for a skilled Frontend Developer to join our team...',
            platform: 'wuzzuf',
            url: 'https://wuzzuf.net/jobs/p/123456-Frontend-Developer',
            date: new Date(Date.now() - 10800000).toISOString(),
            price: '8000-15000 EGP',
            category: 'web'
        },
        {
            id: 'wuzzuf_2',
            title: 'Data Analyst - Business Intelligence',
            description: 'Join our data team as a Data Analyst specializing in BI solutions...',
            platform: 'wuzzuf',
            url: 'https://wuzzuf.net/jobs/p/123457-Data-Analyst',
            date: new Date(Date.now() - 14400000).toISOString(),
            price: '10000-18000 EGP',
            category: 'data'
        }
    ];
}

// Display jobs in the UI
function displayJobs() {
    if (filteredJobs.length === 0) {
        jobsContainer.innerHTML = `
            <div style="text-align: center; padding: 40px; color: #666;">
                <p>🔍 لا توجد وظائف متاحة حالياً</p>
                <p style="font-size: 12px; margin-top: 10px;">جرب تغيير الفلاتر أو إعادة التحديث</p>
            </div>
        `;
        return;
    }
    
    jobsContainer.innerHTML = filteredJobs.map(job => `
        <div class="job-card" onclick="openJobLink('${job.url}')">
            <div class="job-platform ${job.platform}">${getPlatformName(job.platform)}</div>
            <div class="job-title">${job.title}</div>
            <div class="job-description">${job.description}</div>
            <div class="job-meta">
                <span class="job-date">${formatDate(job.date)}</span>
                <span class="job-price">${job.price}</span>
            </div>
        </div>
    `).join('');
}

// Filter jobs based on selected criteria
function filterJobs() {
    const platformValue = platformFilter.value;
    const categoryValue = categoryFilter.value;
    
    filteredJobs = allJobs.filter(job => {
        const platformMatch = platformValue === 'all' || job.platform === platformValue;
        const categoryMatch = categoryValue === 'all' || job.category === categoryValue;
        return platformMatch && categoryMatch;
    });
    
    displayJobs();
    updateStats();
}

// Utility functions
function getPlatformName(platform) {
    const names = {
        'upwork': 'Upwork',
        'khamsat': 'خمسات',
        'wuzzuf': 'وظف'
    };
    return names[platform] || platform;
}

function formatDate(dateString) {
    const date = new Date(dateString);
    const now = new Date();
    const diffMs = now - date;
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffHours / 24);
    
    if (diffHours < 1) return 'منذ أقل من ساعة';
    if (diffHours < 24) return `منذ ${diffHours} ساعة`;
    if (diffDays < 7) return `منذ ${diffDays} يوم`;
    return date.toLocaleDateString('ar-EG');
}

function openJobLink(url) {
    chrome.tabs.create({ url: url });
}

function updateStats() {
    totalJobsEl.textContent = filteredJobs.length;
    lastUpdateEl.textContent = new Date().toLocaleString('ar-EG');
}

function showLoading() {
    loadingEl.style.display = 'block';
    jobsContainer.style.display = 'none';
}

function hideLoading() {
    loadingEl.style.display = 'none';
    jobsContainer.style.display = 'block';
}

function showError() {
    errorEl.style.display = 'block';
    jobsContainer.style.display = 'none';
}

function hideError() {
    errorEl.style.display = 'none';
}
