<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار البيانات الحقيقية فقط</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            direction: rtl;
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            margin: 20px;
            border-radius: 6px;
            text-align: center;
        }

        .content {
            padding: 30px;
        }

        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #eee;
            border-radius: 8px;
            background: #f9f9f9;
        }

        .test-button {
            background: #e74c3c;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .test-button:hover {
            background: #c0392b;
            transform: translateY(-2px);
        }

        .result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 6px;
            background: #e9ecef;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }

        .success {
            background: #d4edda;
            color: #155724;
        }

        .error {
            background: #f8d7da;
            color: #721c24;
        }

        .loading {
            text-align: center;
            padding: 20px;
        }

        .spinner {
            width: 30px;
            height: 30px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #e74c3c;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .stats {
            background: #17a2b8;
            color: white;
            padding: 15px;
            margin: 20px;
            border-radius: 6px;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔥 اختبار البيانات الحقيقية فقط</h1>
            <p>لا توجد بيانات تجريبية - فقط بيانات حقيقية من المواقع</p>
        </div>

        <div class="warning">
            <h3>⚠️ تحذير مهم</h3>
            <p>هذا الاختبار يحاول جلب بيانات حقيقية فقط. إذا فشلت جميع الطرق، لن تظهر أي وظائف.</p>
        </div>

        <div class="stats" id="stats">
            <h3>📊 إحصائيات الاختبار</h3>
            <p>المحاولات الناجحة: <span id="successCount">0</span></p>
            <p>المحاولات الفاشلة: <span id="failCount">0</span></p>
            <p>إجمالي الوظائف: <span id="totalJobs">0</span></p>
        </div>

        <div class="content">
            <!-- اختبار Wuzzuf APIs الحقيقية -->
            <div class="test-section">
                <h2>🇪🇬 Wuzzuf - APIs حقيقية</h2>
                <button class="test-button" id="testWuzzufRealAPI">اختبار Wuzzuf Real API</button>
                <button class="test-button" id="testWuzzufRSS">اختبار Wuzzuf RSS</button>
                <button class="test-button" id="testWuzzufScraping">اختبار Wuzzuf Scraping</button>
                <div id="wuzzufResult" class="result"></div>
            </div>

            <!-- اختبار Upwork APIs الحقيقية -->
            <div class="test-section">
                <h2>🌍 Upwork - APIs حقيقية</h2>
                <button class="test-button" id="testUpworkRealAPI">اختبار Upwork Real API</button>
                <button class="test-button" id="testUpworkRSS">اختبار Upwork RSS</button>
                <button class="test-button" id="testUpworkProxy">اختبار Upwork Proxy</button>
                <div id="upworkResult" class="result"></div>
            </div>

            <!-- اختبار Khamsat Scraping الحقيقي -->
            <div class="test-section">
                <h2>🇸🇦 Khamsat - Scraping حقيقي</h2>
                <button class="test-button" id="testKhamsatReal">اختبار Khamsat Real</button>
                <button class="test-button" id="testKhamsatProxy">اختبار Khamsat Proxy</button>
                <div id="khamsatResult" class="result"></div>
            </div>

            <!-- اختبار شامل -->
            <div class="test-section">
                <h2>🚀 اختبار شامل - بيانات حقيقية فقط</h2>
                <button class="test-button" id="testAllReal">اختبار جميع الطرق الحقيقية</button>
                <button class="test-button" id="clearResults">مسح النتائج</button>
                <div id="allResult" class="result"></div>
            </div>

            <!-- عرض النتائج النهائية -->
            <div class="test-section">
                <h2>📋 الوظائف الحقيقية المجلبة</h2>
                <div id="realJobs"></div>
            </div>
        </div>
    </div>

    <script>
        let successCount = 0;
        let failCount = 0;
        let totalJobs = 0;
        let allRealJobs = [];

        // Update stats
        function updateStats() {
            document.getElementById('successCount').textContent = successCount;
            document.getElementById('failCount').textContent = failCount;
            document.getElementById('totalJobs').textContent = totalJobs;
        }

        // Test Wuzzuf Real API
        async function testWuzzufRealAPI() {
            const result = document.getElementById('wuzzufResult');
            result.innerHTML = '<div class="loading"><div class="spinner"></div>جاري اختبار Wuzzuf Real API...</div>';
            
            const apiEndpoints = [
                'https://wuzzuf.net/api/search/jobs?q=developer&start=0&rows=10',
                'https://wuzzuf.net/search/jobs/api?query=programmer&limit=10',
                'https://api.wuzzuf.net/v1/jobs?search=software&count=10',
                'https://wuzzuf.net/jobs/search?q=web&format=json',
                'https://www.wuzzuf.net/api/jobs/search?keyword=mobile'
            ];
            
            let results = [];
            let foundJobs = 0;
            
            for (const endpoint of apiEndpoints) {
                try {
                    console.log(`Testing: ${endpoint}`);
                    const response = await fetch(endpoint, {
                        headers: {
                            'Accept': 'application/json',
                            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                        }
                    });
                    
                    if (response.ok) {
                        const data = await response.json();
                        const jobsCount = extractJobsCount(data);
                        results.push(`✅ ${endpoint}: نجح (${jobsCount} وظائف)`);
                        foundJobs += jobsCount;
                        successCount++;
                        
                        if (jobsCount > 0) {
                            allRealJobs.push(...parseJobsFromAPI(data, 'wuzzuf'));
                        }
                    } else {
                        results.push(`❌ ${endpoint}: فشل (${response.status})`);
                        failCount++;
                    }
                } catch (error) {
                    results.push(`❌ ${endpoint}: خطأ - ${error.message}`);
                    failCount++;
                }
            }
            
            totalJobs += foundJobs;
            updateStats();
            displayRealJobs();
            
            result.textContent = `نتائج Wuzzuf Real API:\n${results.join('\n')}\n\nإجمالي الوظائف: ${foundJobs}`;
            result.className = foundJobs > 0 ? 'result success' : 'result error';
        }

        // Test Upwork Real API
        async function testUpworkRealAPI() {
            const result = document.getElementById('upworkResult');
            result.innerHTML = '<div class="loading"><div class="spinner"></div>جاري اختبار Upwork Real API...</div>';
            
            const apiEndpoints = [
                'https://www.upwork.com/api/profiles/v1/search/jobs?q=developer',
                'https://api.upwork.com/api/profiles/v1/search/jobs?query=programmer',
                'https://www.upwork.com/ab/jobs/api/search?q=web&sort=recency',
                'https://www.upwork.com/freelance-jobs/api?search=mobile'
            ];
            
            let results = [];
            let foundJobs = 0;
            
            for (const endpoint of apiEndpoints) {
                try {
                    console.log(`Testing: ${endpoint}`);
                    const response = await fetch(endpoint, {
                        headers: {
                            'Accept': 'application/json',
                            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                        }
                    });
                    
                    if (response.ok) {
                        const data = await response.json();
                        const jobsCount = extractJobsCount(data);
                        results.push(`✅ ${endpoint}: نجح (${jobsCount} وظائف)`);
                        foundJobs += jobsCount;
                        successCount++;
                        
                        if (jobsCount > 0) {
                            allRealJobs.push(...parseJobsFromAPI(data, 'upwork'));
                        }
                    } else {
                        results.push(`❌ ${endpoint}: فشل (${response.status})`);
                        failCount++;
                    }
                } catch (error) {
                    results.push(`❌ ${endpoint}: خطأ - ${error.message}`);
                    failCount++;
                }
            }
            
            totalJobs += foundJobs;
            updateStats();
            displayRealJobs();
            
            result.textContent = `نتائج Upwork Real API:\n${results.join('\n')}\n\nإجمالي الوظائف: ${foundJobs}`;
            result.className = foundJobs > 0 ? 'result success' : 'result error';
        }

        // Extract jobs count from API response
        function extractJobsCount(data) {
            if (data.jobs && Array.isArray(data.jobs)) return data.jobs.length;
            if (data.results && Array.isArray(data.results)) return data.results.length;
            if (data.data && data.data.jobs && Array.isArray(data.data.jobs)) return data.data.jobs.length;
            if (Array.isArray(data)) return data.length;
            return 0;
        }

        // Parse jobs from API response
        function parseJobsFromAPI(data, platform) {
            const jobs = [];
            let jobsArray = [];
            
            if (data.jobs) jobsArray = data.jobs;
            else if (data.results) jobsArray = data.results;
            else if (data.data && data.data.jobs) jobsArray = data.data.jobs;
            else if (Array.isArray(data)) jobsArray = data;
            
            jobsArray.slice(0, 5).forEach((job, index) => {
                jobs.push({
                    id: `${platform}_real_${Date.now()}_${index}`,
                    title: job.title || job.jobTitle || job.name || 'عنوان غير متاح',
                    description: (job.description || job.jobDescription || job.summary || 'لا يوجد وصف').substring(0, 100) + '...',
                    platform: platform,
                    url: job.url || job.jobUrl || job.link || `https://${platform}.com`,
                    date: new Date().toISOString(),
                    price: job.budget || job.salary || job.price || 'غير محدد'
                });
            });
            
            return jobs;
        }

        // Display real jobs
        function displayRealJobs() {
            const container = document.getElementById('realJobs');
            
            if (allRealJobs.length === 0) {
                container.innerHTML = '<p style="text-align: center; color: #666;">لا توجد وظائف حقيقية حتى الآن</p>';
                return;
            }
            
            container.innerHTML = allRealJobs.map(job => `
                <div style="border: 1px solid #ddd; padding: 10px; margin: 10px 0; border-radius: 6px; background: white;">
                    <div style="font-weight: bold; color: #2c3e50;">${job.title}</div>
                    <div style="font-size: 12px; color: #666; margin: 5px 0;">${job.description}</div>
                    <div style="display: flex; justify-content: space-between; font-size: 11px;">
                        <span style="background: #4facfe; color: white; padding: 2px 6px; border-radius: 10px;">${job.platform}</span>
                        <span style="color: #e74c3c; font-weight: bold;">${job.price}</span>
                    </div>
                </div>
            `).join('');
        }

        // Event listeners
        document.addEventListener('DOMContentLoaded', () => {
            document.getElementById('testWuzzufRealAPI').addEventListener('click', testWuzzufRealAPI);
            document.getElementById('testUpworkRealAPI').addEventListener('click', testUpworkRealAPI);
            
            document.getElementById('testWuzzufRSS').addEventListener('click', () => {
                document.getElementById('wuzzufResult').textContent = 'جاري التطوير...';
            });
            
            document.getElementById('testWuzzufScraping').addEventListener('click', () => {
                document.getElementById('wuzzufResult').textContent = 'جاري التطوير...';
            });
            
            document.getElementById('testUpworkRSS').addEventListener('click', () => {
                document.getElementById('upworkResult').textContent = 'جاري التطوير...';
            });
            
            document.getElementById('testUpworkProxy').addEventListener('click', () => {
                document.getElementById('upworkResult').textContent = 'جاري التطوير...';
            });
            
            document.getElementById('testKhamsatReal').addEventListener('click', () => {
                document.getElementById('khamsatResult').textContent = 'جاري التطوير...';
            });
            
            document.getElementById('testKhamsatProxy').addEventListener('click', () => {
                document.getElementById('khamsatResult').textContent = 'جاري التطوير...';
            });
            
            document.getElementById('testAllReal').addEventListener('click', async () => {
                await testWuzzufRealAPI();
                await testUpworkRealAPI();
            });
            
            document.getElementById('clearResults').addEventListener('click', () => {
                successCount = 0;
                failCount = 0;
                totalJobs = 0;
                allRealJobs = [];
                updateStats();
                displayRealJobs();
                document.querySelectorAll('.result').forEach(el => el.textContent = '');
            });
            
            console.log('صفحة اختبار البيانات الحقيقية فقط جاهزة');
        });
    </script>
</body>
</html>
