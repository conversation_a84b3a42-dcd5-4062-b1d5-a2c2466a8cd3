# دليل التثبيت والاستخدام
## Tech Jobs Finder Chrome Extension

## 📋 متطلبات النظام

- Google Chrome 88+ أو Microsoft Edge 88+
- نظام التشغيل: Windows, macOS, أو Linux
- اتصال بالإنترنت

## 🚀 خطوات التثبيت

### الخطوة 1: تحضير الملفات

1. **تحميل المشروع:**
   ```bash
   git clone [repository-url]
   cd Extension
   ```

2. **إنشاء الأيقونات:**
   - افتح ملف `icons/create_icons.html` في المتصفح
   - ستتم تحميل الأيقونات تلقائياً
   - انقل الأيقونات إلى مجلد `icons/`

### الخطوة 2: تثبيت الإضافة في Chrome

1. **فتح صفحة الإضافات:**
   - اذهب إلى `chrome://extensions/`
   - أو من القائمة: المزيد من الأدوات > الإضافات

2. **تفعيل وضع المطور:**
   - فعّل "Developer mode" في الزاوية العلوية اليمنى

3. **تحميل الإضافة:**
   - اضغط "Load unpacked"
   - اختر مجلد المشروع `Extension`
   - اضغط "Select Folder"

4. **التحقق من التثبيت:**
   - ستظهر الإضافة في قائمة الإضافات
   - ستظهر أيقونة 🚀 في شريط الأدوات

## 🎯 الاستخدام الأساسي

### فتح الإضافة
- اضغط على أيقونة 🚀 في شريط الأدوات
- أو استخدم اختصار لوحة المفاتيح (إذا تم تعيينه)

### تصفح الوظائف
1. **التحديث:** اضغط زر "تحديث" لجلب أحدث الوظائف
2. **الفلترة:** استخدم القوائم المنسدلة لفلترة النتائج
3. **فتح الوظيفة:** اضغط على أي وظيفة لفتحها في تبويب جديد

### استخدام الفلاتر
- **المنصة:** اختر منصة محددة أو "جميع المنصات"
- **التخصص:** اختر تخصص محدد أو "جميع التخصصات"

## ⚙️ الإعدادات المتقدمة

### فتح صفحة الإعدادات
1. انقر بزر الماوس الأيمن على أيقونة الإضافة
2. اختر "Options" أو "خيارات"
3. أو اذهب إلى `chrome://extensions/` واضغط "Details" ثم "Extension options"

### الإعدادات المتاحة

#### الإعدادات العامة
- **الحد الأقصى للوظائف:** 5-20 وظيفة
- **فترة التحديث:** 15 دقيقة - ساعتان
- **التحديث التلقائي:** تفعيل/إيقاف
- **التنبيهات:** تفعيل/إيقاف

#### إعدادات المنصات
- تفعيل/إيقاف منصات محددة
- Upwork, خمسات, وظف

#### فلاتر البحث
- **التخصصات المفضلة:** اختيار متعدد
- **كلمات مفتاحية:** للبحث المتقدم
- **كلمات الاستبعاد:** لتجنب وظائف معينة

#### إعدادات متقدمة
- **التخزين المؤقت:** تفعيل/إيقاف
- **مدة انتهاء الصلاحية:** 1-24 ساعة
- **وضع التطوير:** للمطورين

## 🔧 استكشاف الأخطاء وإصلاحها

### مشاكل شائعة وحلولها

#### 1. الإضافة لا تظهر
**الأعراض:** لا توجد أيقونة في شريط الأدوات
**الحلول:**
- تأكد من تثبيت الإضافة بشكل صحيح
- أعد تحميل الإضافة من `chrome://extensions/`
- تحقق من وجود جميع الملفات المطلوبة

#### 2. لا تظهر وظائف
**الأعراض:** رسالة "لا توجد وظائف متاحة"
**الحلول:**
- تحقق من اتصال الإنترنت
- اضغط زر "تحديث"
- تحقق من إعدادات الفلاتر
- افتح Developer Tools وتحقق من الأخطاء

#### 3. خطأ CORS
**الأعراض:** رسائل خطأ في Console
**الحلول:**
- هذا متوقع للمواقع التي تمنع الوصول المباشر
- الإضافة تستخدم بيانات تجريبية كبديل
- للحصول على بيانات حقيقية، يجب استخدام APIs رسمية

#### 4. الإضافة بطيئة
**الأعراض:** تحميل بطيء للوظائف
**الحلول:**
- قلل عدد الوظائف المعروضة في الإعدادات
- فعّل التخزين المؤقت
- أغلق التبويبات غير المستخدمة

### فحص الأخطاء

#### فتح Developer Tools
1. انقر بزر الماوس الأيمن على أيقونة الإضافة
2. اختر "Inspect popup"
3. أو اضغط F12 في صفحة الإضافة

#### فحص Background Script
1. اذهب إلى `chrome://extensions/`
2. اضغط "Details" على الإضافة
3. اضغط "Inspect views: background page"

#### فحص Console
- ابحث عن رسائل الخطأ باللون الأحمر
- تحقق من رسائل التحذير باللون الأصفر
- راجع رسائل المعلومات للتأكد من سير العمل

## 🧪 اختبار الإضافة

### استخدام صفحة الاختبار
1. افتح ملف `test.html` في المتصفح
2. اتبع التعليمات في الصفحة
3. اختبر جميع الوظائف

### اختبارات يدوية
- [ ] فتح الإضافة من شريط الأدوات
- [ ] تحديث الوظائف
- [ ] استخدام الفلاتر
- [ ] فتح وظيفة في تبويب جديد
- [ ] فتح صفحة الإعدادات
- [ ] حفظ وتحميل الإعدادات

## 📊 مراقبة الأداء

### إحصائيات الاستخدام
- عدد الوظائف المحملة
- آخر وقت تحديث
- المنصات النشطة

### تحسين الأداء
- استخدم التخزين المؤقت
- قلل فترة التحديث التلقائي
- أوقف المنصات غير المستخدمة

## 🔄 التحديثات

### تحديث الإضافة
1. احفظ إعداداتك (تصدير)
2. حمّل النسخة الجديدة
3. أعد تثبيت الإضافة
4. استورد إعداداتك

### النسخ الاحتياطية
- صدّر إعداداتك بانتظام
- احفظ نسخة من مجلد الإضافة

## 🆘 الحصول على المساعدة

### الموارد المتاحة
- ملف README.md للمعلومات العامة
- ملف test.html لاختبار الوظائف
- Developer Tools للتشخيص

### الإبلاغ عن المشاكل
عند الإبلاغ عن مشكلة، يرجى تضمين:
- نسخة Chrome المستخدمة
- نظام التشغيل
- خطوات إعادة إنتاج المشكلة
- رسائل الخطأ من Console
- لقطات شاشة إن أمكن

### معلومات إضافية
- تحقق من شروط الخدمة للمواقع المستهدفة
- احترم حدود الاستخدام المعقولة
- لا تستخدم الإضافة لأغراض تجارية بدون إذن

---

**ملاحظة مهمة:** هذه الإضافة للأغراض التعليمية والشخصية. تأكد من احترام شروط الخدمة للمواقع المستهدفة.
