// Content script for injecting functionality into web pages
console.log('Tech Jobs Finder content script loaded');

// Listen for messages from popup or background script
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    console.log('Content script received message:', request);
    
    switch (request.action) {
        case 'scrapeJobs':
            scrapeCurrentPage()
                .then(jobs => sendResponse({ success: true, data: jobs }))
                .catch(error => sendResponse({ success: false, error: error.message }));
            return true; // Keep message channel open for async response
            
        case 'checkPageType':
            const pageType = detectPageType();
            sendResponse({ success: true, pageType: pageType });
            break;
            
        default:
            sendResponse({ success: false, error: 'Unknown action' });
    }
});

// Detect what type of job site we're on
function detectPageType() {
    const hostname = window.location.hostname.toLowerCase();
    
    if (hostname.includes('upwork.com')) {
        return 'upwork';
    } else if (hostname.includes('khamsat.com')) {
        return 'khamsat';
    } else if (hostname.includes('wuzzuf.net')) {
        return 'wuzzuf';
    } else {
        return 'unknown';
    }
}

// Scrape jobs from the current page
async function scrapeCurrentPage() {
    const pageType = detectPageType();
    console.log(`Scraping ${pageType} page...`);
    
    switch (pageType) {
        case 'upwork':
            return scrapeUpworkJobs();
        case 'khamsat':
            return scrapeKhamsatJobs();
        case 'wuzzuf':
            return scrapeWuzzufJobs();
        default:
            throw new Error('Unsupported page type');
    }
}

// Scrape Upwork jobs from current page
function scrapeUpworkJobs() {
    const jobs = [];
    
    try {
        // Look for job tiles on Upwork
        const jobElements = document.querySelectorAll('[data-test="JobTile"], .job-tile, .up-card-section');
        
        jobElements.forEach((element, index) => {
            if (index >= 5) return; // Limit to 5 jobs
            
            try {
                // Try different selectors for title
                const titleElement = element.querySelector('[data-test="JobTileTitle"] a, .job-tile-title a, h4 a, h3 a');
                const descElement = element.querySelector('[data-test="JobTileDescription"], .job-tile-description, .up-line-height-reset');
                const priceElement = element.querySelector('[data-test="JobTileBudget"], .job-tile-budget, .budget');
                
                if (titleElement) {
                    const title = titleElement.textContent.trim();
                    const description = descElement ? descElement.textContent.trim().substring(0, 150) + '...' : 'لا يوجد وصف متاح';
                    const price = priceElement ? priceElement.textContent.trim() : 'غير محدد';
                    const url = titleElement.href || window.location.href;
                    
                    jobs.push({
                        id: `upwork_scraped_${Date.now()}_${index}`,
                        title: title,
                        description: description,
                        platform: 'upwork',
                        url: url,
                        date: new Date().toISOString(),
                        price: price,
                        category: detectJobCategory(title + ' ' + description)
                    });
                }
            } catch (error) {
                console.error('Error parsing Upwork job element:', error);
            }
        });
        
    } catch (error) {
        console.error('Error scraping Upwork jobs:', error);
    }
    
    return jobs;
}

// Scrape Khamsat jobs from current page
function scrapeKhamsatJobs() {
    const jobs = [];
    
    try {
        // Look for service cards on Khamsat
        const serviceElements = document.querySelectorAll('.service-card, .gig-card, .service-item');
        
        serviceElements.forEach((element, index) => {
            if (index >= 5) return; // Limit to 5 jobs
            
            try {
                const titleElement = element.querySelector('.service-title a, .gig-title a, h3 a, h4 a');
                const descElement = element.querySelector('.service-description, .gig-description, .description');
                const priceElement = element.querySelector('.service-price, .gig-price, .price');
                
                if (titleElement) {
                    const title = titleElement.textContent.trim();
                    const description = descElement ? descElement.textContent.trim().substring(0, 150) + '...' : 'لا يوجد وصف متاح';
                    const price = priceElement ? priceElement.textContent.trim() : 'غير محدد';
                    const url = titleElement.href || window.location.href;
                    
                    jobs.push({
                        id: `khamsat_scraped_${Date.now()}_${index}`,
                        title: title,
                        description: description,
                        platform: 'khamsat',
                        url: url,
                        date: new Date().toISOString(),
                        price: price,
                        category: detectJobCategory(title + ' ' + description)
                    });
                }
            } catch (error) {
                console.error('Error parsing Khamsat service element:', error);
            }
        });
        
    } catch (error) {
        console.error('Error scraping Khamsat jobs:', error);
    }
    
    return jobs;
}

// Scrape Wuzzuf jobs from current page
function scrapeWuzzufJobs() {
    const jobs = [];
    
    try {
        // Look for job cards on Wuzzuf
        const jobElements = document.querySelectorAll('.css-1gatmva, .job-card, .search-result');
        
        jobElements.forEach((element, index) => {
            if (index >= 5) return; // Limit to 5 jobs
            
            try {
                const titleElement = element.querySelector('h2 a, .job-title a, h3 a');
                const descElement = element.querySelector('.css-y4udm8, .job-description, .description');
                const companyElement = element.querySelector('.css-d7j1kk, .company-name');
                const salaryElement = element.querySelector('.css-4xky9y, .salary');
                
                if (titleElement) {
                    const title = titleElement.textContent.trim();
                    const description = descElement ? descElement.textContent.trim().substring(0, 150) + '...' : 'لا يوجد وصف متاح';
                    const company = companyElement ? companyElement.textContent.trim() : '';
                    const salary = salaryElement ? salaryElement.textContent.trim() : 'غير محدد';
                    const url = titleElement.href || window.location.href;
                    
                    jobs.push({
                        id: `wuzzuf_scraped_${Date.now()}_${index}`,
                        title: title,
                        description: description + (company ? ` - ${company}` : ''),
                        platform: 'wuzzuf',
                        url: url,
                        date: new Date().toISOString(),
                        price: salary,
                        category: detectJobCategory(title + ' ' + description)
                    });
                }
            } catch (error) {
                console.error('Error parsing Wuzzuf job element:', error);
            }
        });
        
    } catch (error) {
        console.error('Error scraping Wuzzuf jobs:', error);
    }
    
    return jobs;
}

// Detect job category based on title and description
function detectJobCategory(text) {
    const lowerText = text.toLowerCase();
    
    if (lowerText.includes('react') || lowerText.includes('vue') || lowerText.includes('angular') || 
        lowerText.includes('frontend') || lowerText.includes('web') || lowerText.includes('html') ||
        lowerText.includes('css') || lowerText.includes('javascript') || lowerText.includes('ويب') ||
        lowerText.includes('موقع')) {
        return 'web';
    }
    
    if (lowerText.includes('mobile') || lowerText.includes('android') || lowerText.includes('ios') ||
        lowerText.includes('flutter') || lowerText.includes('react native') || lowerText.includes('swift') ||
        lowerText.includes('kotlin') || lowerText.includes('تطبيق') || lowerText.includes('جوال')) {
        return 'mobile';
    }
    
    if (lowerText.includes('data') || lowerText.includes('python') || lowerText.includes('machine learning') ||
        lowerText.includes('ai') || lowerText.includes('analytics') || lowerText.includes('sql') ||
        lowerText.includes('بيانات') || lowerText.includes('تحليل')) {
        return 'data';
    }
    
    if (lowerText.includes('design') || lowerText.includes('ui') || lowerText.includes('ux') ||
        lowerText.includes('figma') || lowerText.includes('photoshop') || lowerText.includes('تصميم') ||
        lowerText.includes('جرافيك')) {
        return 'design';
    }
    
    return 'web'; // Default category
}

// Add a floating button for quick access (optional)
function addFloatingButton() {
    // Only add on job sites
    const pageType = detectPageType();
    if (pageType === 'unknown') return;
    
    const button = document.createElement('div');
    button.innerHTML = '🚀';
    button.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        width: 50px;
        height: 50px;
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        z-index: 10000;
        box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        font-size: 20px;
        transition: all 0.3s ease;
    `;
    
    button.addEventListener('click', () => {
        chrome.runtime.sendMessage({ action: 'openPopup' });
    });
    
    button.addEventListener('mouseenter', () => {
        button.style.transform = 'scale(1.1)';
    });
    
    button.addEventListener('mouseleave', () => {
        button.style.transform = 'scale(1)';
    });
    
    document.body.appendChild(button);
}

// Initialize content script
function init() {
    console.log('Initializing content script...');
    
    // Add floating button after page loads
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', addFloatingButton);
    } else {
        addFloatingButton();
    }
}

// Start initialization
init();
