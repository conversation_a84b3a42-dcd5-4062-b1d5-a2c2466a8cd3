// Background script for handling CORS and API requests
console.log('Background script loaded');

// Handle extension installation
chrome.runtime.onInstalled.addListener((details) => {
    console.log('Extension installed:', details.reason);
    
    if (details.reason === 'install') {
        // Set default settings
        chrome.storage.local.set({
            settings: {
                autoRefresh: true,
                refreshInterval: 30, // minutes
                notifications: true,
                maxJobs: 15
            }
        });
    }
});

// Handle messages from popup
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    console.log('Background received message:', request);
    
    switch (request.action) {
        case 'fetchJobs':
            handleFetchJobs(request.platform)
                .then(jobs => sendResponse({ success: true, data: jobs }))
                .catch(error => sendResponse({ success: false, error: error.message }));
            return true; // Keep message channel open for async response
            
        case 'openTab':
            chrome.tabs.create({ url: request.url });
            sendResponse({ success: true });
            break;
            
        default:
            sendResponse({ success: false, error: 'Unknown action' });
    }
});

// Fetch jobs from different platforms
async function handleFetchJobs(platform) {
    console.log(`Fetching jobs from ${platform}`);
    
    try {
        switch (platform) {
            case 'upwork':
                return await fetchUpworkJobs();
            case 'khamsat':
                return await fetchKhamsatJobs();
            case 'wuzzuf':
                return await fetchWuzzufJobs();
            default:
                throw new Error('Unknown platform');
        }
    } catch (error) {
        console.error(`Error fetching ${platform} jobs:`, error);
        throw error;
    }
}

// Fetch Upwork jobs using alternative methods
async function fetchUpworkJobs() {
    // Since Upwork doesn't provide RSS and has strict CORS policies,
    // we'll use a proxy service or return mock data
    
    try {
        // Option 1: Try using a CORS proxy (may not always work)
        const proxyUrl = 'https://cors-anywhere.herokuapp.com/';
        const targetUrl = 'https://www.upwork.com/ab/jobs/search/?q=web%20development&sort=recency';
        
        const response = await fetch(proxyUrl + targetUrl, {
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        });
        
        if (!response.ok) {
            throw new Error('Failed to fetch from Upwork');
        }
        
        const html = await response.text();
        return parseUpworkHTML(html);
        
    } catch (error) {
        console.log('Falling back to mock Upwork data');
        return getMockUpworkJobs();
    }
}

// Parse Upwork HTML (simplified version)
function parseUpworkHTML(html) {
    const parser = new DOMParser();
    const doc = parser.parseFromString(html, 'text/html');
    
    const jobs = [];
    const jobElements = doc.querySelectorAll('[data-test="JobTile"]');
    
    jobElements.forEach((element, index) => {
        if (index >= 5) return; // Limit to 5 jobs
        
        try {
            const titleElement = element.querySelector('[data-test="JobTileTitle"] a');
            const descElement = element.querySelector('[data-test="JobTileDescription"]');
            const priceElement = element.querySelector('[data-test="JobTileBudget"]');
            const dateElement = element.querySelector('[data-test="JobTilePostedOn"]');
            
            if (titleElement && descElement) {
                jobs.push({
                    id: `upwork_${Date.now()}_${index}`,
                    title: titleElement.textContent.trim(),
                    description: descElement.textContent.trim().substring(0, 150) + '...',
                    platform: 'upwork',
                    url: 'https://www.upwork.com' + titleElement.getAttribute('href'),
                    date: new Date().toISOString(),
                    price: priceElement ? priceElement.textContent.trim() : 'غير محدد',
                    category: 'web'
                });
            }
        } catch (error) {
            console.error('Error parsing job element:', error);
        }
    });
    
    return jobs;
}

// Mock Upwork jobs for demonstration
function getMockUpworkJobs() {
    return [
        {
            id: `upwork_${Date.now()}_1`,
            title: 'Full Stack Developer - MERN Stack',
            description: 'Looking for an experienced full stack developer to build a modern e-commerce platform using React, Node.js, Express, and MongoDB...',
            platform: 'upwork',
            url: 'https://www.upwork.com/jobs/~01234567890',
            date: new Date().toISOString(),
            price: '$25-50/hr',
            category: 'web'
        },
        {
            id: `upwork_${Date.now()}_2`,
            title: 'React Native Mobile App Developer',
            description: 'Need a skilled React Native developer to create a cross-platform mobile application for iOS and Android...',
            platform: 'upwork',
            url: 'https://www.upwork.com/jobs/~01234567891',
            date: new Date(Date.now() - 3600000).toISOString(),
            price: '$30-60/hr',
            category: 'mobile'
        },
        {
            id: `upwork_${Date.now()}_3`,
            title: 'Python Data Scientist - Machine Learning',
            description: 'Seeking a data scientist with expertise in Python, pandas, scikit-learn, and TensorFlow for predictive analytics project...',
            platform: 'upwork',
            url: 'https://www.upwork.com/jobs/~01234567892',
            date: new Date(Date.now() - 7200000).toISOString(),
            price: '$40-80/hr',
            category: 'data'
        },
        {
            id: `upwork_${Date.now()}_4`,
            title: 'UI/UX Designer - Figma Expert',
            description: 'Looking for a creative UI/UX designer to design modern and user-friendly interfaces for our web application...',
            platform: 'upwork',
            url: 'https://www.upwork.com/jobs/~01234567893',
            date: new Date(Date.now() - 10800000).toISOString(),
            price: '$20-40/hr',
            category: 'design'
        },
        {
            id: `upwork_${Date.now()}_5`,
            title: 'WordPress Developer - Custom Theme',
            description: 'Need an experienced WordPress developer to create a custom theme and implement advanced functionality...',
            platform: 'upwork',
            url: 'https://www.upwork.com/jobs/~01234567894',
            date: new Date(Date.now() - 14400000).toISOString(),
            price: '$15-35/hr',
            category: 'web'
        }
    ];
}

// Fetch Khamsat jobs (mock data - real implementation would scrape the site)
async function fetchKhamsatJobs() {
    // Mock data for Khamsat
    return [
        {
            id: `khamsat_${Date.now()}_1`,
            title: 'تطوير موقع إلكتروني متجاوب بـ HTML/CSS/JS',
            description: 'مطلوب مطور ويب محترف لإنشاء موقع إلكتروني متجاوب وحديث للشركة مع تصميم عصري وسرعة تحميل عالية...',
            platform: 'khamsat',
            url: 'https://khamsat.com/programming/web/123456',
            date: new Date(Date.now() - 1800000).toISOString(),
            price: '$200-500',
            category: 'web'
        },
        {
            id: `khamsat_${Date.now()}_2`,
            title: 'تصميم تطبيق جوال - UI/UX Design',
            description: 'نحتاج مصمم محترف لتصميم واجهات تطبيق جوال حديث ومبتكر مع تجربة مستخدم ممتازة...',
            platform: 'khamsat',
            url: 'https://khamsat.com/design/mobile/123457',
            date: new Date(Date.now() - 5400000).toISOString(),
            price: '$150-300',
            category: 'design'
        },
        {
            id: `khamsat_${Date.now()}_3`,
            title: 'برمجة تطبيق Flutter متعدد المنصات',
            description: 'مطلوب مطور Flutter خبير لبرمجة تطبيق جوال متعدد المنصات مع ميزات متقدمة وأداء عالي...',
            platform: 'khamsat',
            url: 'https://khamsat.com/programming/mobile/123458',
            date: new Date(Date.now() - 9000000).toISOString(),
            price: '$400-800',
            category: 'mobile'
        }
    ];
}

// Fetch Wuzzuf jobs (mock data - real implementation would scrape the site)
async function fetchWuzzufJobs() {
    // Mock data for Wuzzuf
    return [
        {
            id: `wuzzuf_${Date.now()}_1`,
            title: 'Frontend Developer - React.js & TypeScript',
            description: 'We are looking for a skilled Frontend Developer to join our team and work on cutting-edge web applications using React.js and TypeScript...',
            platform: 'wuzzuf',
            url: 'https://wuzzuf.net/jobs/p/123456-Frontend-Developer',
            date: new Date(Date.now() - 10800000).toISOString(),
            price: '8000-15000 EGP',
            category: 'web'
        },
        {
            id: `wuzzuf_${Date.now()}_2`,
            title: 'Data Analyst - Business Intelligence',
            description: 'Join our data team as a Data Analyst specializing in BI solutions, data visualization, and advanced analytics...',
            platform: 'wuzzuf',
            url: 'https://wuzzuf.net/jobs/p/123457-Data-Analyst',
            date: new Date(Date.now() - 14400000).toISOString(),
            price: '10000-18000 EGP',
            category: 'data'
        },
        {
            id: `wuzzuf_${Date.now()}_3`,
            title: 'Mobile App Developer - Android/iOS',
            description: 'Seeking an experienced mobile app developer to create native applications for Android and iOS platforms...',
            platform: 'wuzzuf',
            url: 'https://wuzzuf.net/jobs/p/123458-Mobile-Developer',
            date: new Date(Date.now() - 18000000).toISOString(),
            price: '12000-20000 EGP',
            category: 'mobile'
        }
    ];
}

// Auto-refresh functionality
chrome.alarms.onAlarm.addListener((alarm) => {
    if (alarm.name === 'autoRefresh') {
        console.log('Auto-refreshing jobs...');
        // Trigger job refresh
        chrome.runtime.sendMessage({ action: 'autoRefresh' });
    }
});

// Set up auto-refresh alarm
chrome.storage.local.get(['settings'], (result) => {
    if (result.settings && result.settings.autoRefresh) {
        chrome.alarms.create('autoRefresh', {
            delayInMinutes: result.settings.refreshInterval,
            periodInMinutes: result.settings.refreshInterval
        });
    }
});
