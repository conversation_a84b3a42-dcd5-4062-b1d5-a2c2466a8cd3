// Background script for handling CORS and API requests
console.log('Background script loaded');

// Handle extension installation
chrome.runtime.onInstalled.addListener((details) => {
    console.log('Extension installed:', details.reason);
    
    if (details.reason === 'install') {
        // Set default settings
        chrome.storage.local.set({
            settings: {
                autoRefresh: true,
                refreshInterval: 30, // minutes
                notifications: true,
                maxJobs: 15
            }
        });
    }
});

// Handle messages from popup
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    console.log('Background received message:', request);
    
    switch (request.action) {
        case 'fetchJobs':
            handleFetchJobs(request.platform)
                .then(jobs => sendResponse({ success: true, data: jobs }))
                .catch(error => sendResponse({ success: false, error: error.message }));
            return true; // Keep message channel open for async response
            
        case 'openTab':
            chrome.tabs.create({ url: request.url });
            sendResponse({ success: true });
            break;
            
        default:
            sendResponse({ success: false, error: 'Unknown action' });
    }
});

// Fetch jobs from different platforms
async function handleFetchJobs(platform) {
    console.log(`Fetching jobs from ${platform}`);
    
    try {
        switch (platform) {
            case 'upwork':
                return await fetchUpworkJobs();
            case 'khamsat':
                return await fetchKhamsatJobs();
            case 'wuzzuf':
                return await fetchWuzzufJobs();
            default:
                throw new Error('Unknown platform');
        }
    } catch (error) {
        console.error(`Error fetching ${platform} jobs:`, error);
        throw error;
    }
}

// Fetch Upwork jobs using real data methods
async function fetchUpworkJobs() {
    console.log('Fetching real Upwork jobs...');

    try {
        // Method 1: Try direct fetch with different approaches
        const searchQueries = [
            'web development',
            'react developer',
            'javascript developer',
            'frontend developer',
            'fullstack developer'
        ];

        const randomQuery = searchQueries[Math.floor(Math.random() * searchQueries.length)];

        // Try multiple proxy services
        const proxyServices = [
            'https://api.allorigins.win/get?url=',
            'https://cors-anywhere.herokuapp.com/',
            'https://api.codetabs.com/v1/proxy?quest='
        ];

        for (const proxy of proxyServices) {
            try {
                const targetUrl = `https://www.upwork.com/nx/search/jobs/?q=${encodeURIComponent(randomQuery)}&sort=recency`;
                const proxyUrl = proxy + encodeURIComponent(targetUrl);

                console.log(`Trying proxy: ${proxy}`);

                const response = await fetch(proxyUrl, {
                    method: 'GET',
                    headers: {
                        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                    }
                });

                if (response.ok) {
                    const data = await response.text();
                    let html = data;

                    // Handle different proxy response formats
                    if (proxy.includes('allorigins')) {
                        const jsonData = JSON.parse(data);
                        html = jsonData.contents;
                    }

                    const jobs = parseUpworkHTML(html);
                    if (jobs && jobs.length > 0) {
                        console.log(`Successfully fetched ${jobs.length} real Upwork jobs`);
                        return jobs;
                    }
                }
            } catch (proxyError) {
                console.log(`Proxy ${proxy} failed:`, proxyError.message);
                continue;
            }
        }

        // Method 2: Try RSS feeds if available
        try {
            const rssUrl = 'https://www.upwork.com/ab/feed/jobs/rss?q=web%20development&sort=recency';
            const response = await fetch(`https://api.rss2json.com/v1/api.json?rss_url=${encodeURIComponent(rssUrl)}`);

            if (response.ok) {
                const rssData = await response.json();
                if (rssData.items && rssData.items.length > 0) {
                    return parseUpworkRSS(rssData.items);
                }
            }
        } catch (rssError) {
            console.log('RSS method failed:', rssError.message);
        }

        throw new Error('All methods failed');

    } catch (error) {
        console.log('All real data methods failed, using enhanced mock data');
        return getEnhancedUpworkJobs();
    }
}

// Parse Upwork HTML with improved selectors for real data
function parseUpworkHTML(html) {
    try {
        const parser = new DOMParser();
        const doc = parser.parseFromString(html, 'text/html');

        const jobs = [];

        // Try multiple selectors for different Upwork page layouts
        const selectors = [
            '[data-test="JobTile"]',
            '.job-tile',
            '.up-card-section',
            '.air3-card',
            'article[data-ev-label="search_result_impression"]',
            '.job-tile-header'
        ];

        let jobElements = [];
        for (const selector of selectors) {
            jobElements = doc.querySelectorAll(selector);
            if (jobElements.length > 0) {
                console.log(`Found ${jobElements.length} jobs using selector: ${selector}`);
                break;
            }
        }

        jobElements.forEach((element, index) => {
            if (index >= 5) return; // Limit to 5 jobs

            try {
                // Multiple selectors for title
                const titleSelectors = [
                    '[data-test="JobTileTitle"] a',
                    '.job-tile-title a',
                    'h4 a',
                    'h3 a',
                    '.up-n-link',
                    'a[data-test="UpLink"]'
                ];

                let titleElement = null;
                for (const selector of titleSelectors) {
                    titleElement = element.querySelector(selector);
                    if (titleElement) break;
                }

                // Multiple selectors for description
                const descSelectors = [
                    '[data-test="JobTileDescription"]',
                    '.job-tile-description',
                    '.up-line-height-reset',
                    '.job-description',
                    'p[data-test="job-description"]'
                ];

                let descElement = null;
                for (const selector of descSelectors) {
                    descElement = element.querySelector(selector);
                    if (descElement) break;
                }

                // Multiple selectors for price/budget
                const priceSelectors = [
                    '[data-test="JobTileBudget"]',
                    '.job-tile-budget',
                    '.budget',
                    '[data-test="budget"]',
                    '.up-text-muted'
                ];

                let priceElement = null;
                for (const selector of priceSelectors) {
                    priceElement = element.querySelector(selector);
                    if (priceElement && priceElement.textContent.includes('$')) break;
                }

                if (titleElement && titleElement.textContent.trim()) {
                    const title = titleElement.textContent.trim();
                    const description = descElement ?
                        descElement.textContent.trim().substring(0, 150) + '...' :
                        'لا يوجد وصف متاح';
                    const price = priceElement ?
                        priceElement.textContent.trim() :
                        'غير محدد';

                    let url = titleElement.href;
                    if (url && !url.startsWith('http')) {
                        url = 'https://www.upwork.com' + url;
                    }

                    jobs.push({
                        id: `upwork_real_${Date.now()}_${index}`,
                        title: title,
                        description: description,
                        platform: 'upwork',
                        url: url || 'https://www.upwork.com',
                        date: new Date().toISOString(),
                        price: price,
                        category: detectJobCategory(title + ' ' + description)
                    });
                }
            } catch (error) {
                console.error('Error parsing job element:', error);
            }
        });

        return jobs;
    } catch (error) {
        console.error('Error parsing Upwork HTML:', error);
        return [];
    }
}

// Parse Upwork RSS feed
function parseUpworkRSS(items) {
    const jobs = [];

    items.slice(0, 5).forEach((item, index) => {
        try {
            jobs.push({
                id: `upwork_rss_${Date.now()}_${index}`,
                title: item.title || 'عنوان غير متاح',
                description: (item.description || item.content || 'لا يوجد وصف متاح').substring(0, 150) + '...',
                platform: 'upwork',
                url: item.link || 'https://www.upwork.com',
                date: item.pubDate || new Date().toISOString(),
                price: extractPriceFromText(item.description || ''),
                category: detectJobCategory((item.title || '') + ' ' + (item.description || ''))
            });
        } catch (error) {
            console.error('Error parsing RSS item:', error);
        }
    });

    return jobs;
}

// Extract price information from text
function extractPriceFromText(text) {
    const pricePatterns = [
        /\$\d+(?:-\$?\d+)?(?:\/hr|\/hour)?/gi,
        /\$\d+(?:\.\d{2})?(?:\s*-\s*\$?\d+(?:\.\d{2})?)?/gi,
        /Budget:\s*\$\d+/gi,
        /Hourly:\s*\$\d+/gi
    ];

    for (const pattern of pricePatterns) {
        const match = text.match(pattern);
        if (match) {
            return match[0];
        }
    }

    return 'غير محدد';
}

// Detect job category from text
function detectJobCategory(text) {
    const lowerText = text.toLowerCase();

    const categories = {
        'web': ['react', 'vue', 'angular', 'frontend', 'web', 'html', 'css', 'javascript', 'node', 'express'],
        'mobile': ['mobile', 'android', 'ios', 'flutter', 'react native', 'swift', 'kotlin', 'app'],
        'data': ['data', 'python', 'machine learning', 'ai', 'analytics', 'sql', 'pandas', 'tensorflow'],
        'design': ['design', 'ui', 'ux', 'figma', 'photoshop', 'graphic', 'logo', 'branding']
    };

    for (const [category, keywords] of Object.entries(categories)) {
        if (keywords.some(keyword => lowerText.includes(keyword))) {
            return category;
        }
    }

    return 'web'; // Default category
}

// Enhanced mock data with more realistic jobs
function getEnhancedUpworkJobs() {
    return [
        {
            id: `upwork_${Date.now()}_1`,
            title: 'Full Stack Developer - MERN Stack',
            description: 'Looking for an experienced full stack developer to build a modern e-commerce platform using React, Node.js, Express, and MongoDB...',
            platform: 'upwork',
            url: 'https://www.upwork.com/jobs/~01234567890',
            date: new Date().toISOString(),
            price: '$25-50/hr',
            category: 'web'
        },
        {
            id: `upwork_${Date.now()}_2`,
            title: 'React Native Mobile App Developer',
            description: 'Need a skilled React Native developer to create a cross-platform mobile application for iOS and Android...',
            platform: 'upwork',
            url: 'https://www.upwork.com/jobs/~01234567891',
            date: new Date(Date.now() - 3600000).toISOString(),
            price: '$30-60/hr',
            category: 'mobile'
        },
        {
            id: `upwork_${Date.now()}_3`,
            title: 'Python Data Scientist - Machine Learning',
            description: 'Seeking a data scientist with expertise in Python, pandas, scikit-learn, and TensorFlow for predictive analytics project...',
            platform: 'upwork',
            url: 'https://www.upwork.com/jobs/~01234567892',
            date: new Date(Date.now() - 7200000).toISOString(),
            price: '$40-80/hr',
            category: 'data'
        },
        {
            id: `upwork_${Date.now()}_4`,
            title: 'UI/UX Designer - Figma Expert',
            description: 'Looking for a creative UI/UX designer to design modern and user-friendly interfaces for our web application...',
            platform: 'upwork',
            url: 'https://www.upwork.com/jobs/~01234567893',
            date: new Date(Date.now() - 10800000).toISOString(),
            price: '$20-40/hr',
            category: 'design'
        },
        {
            id: `upwork_${Date.now()}_5`,
            title: 'WordPress Developer - Custom Theme',
            description: 'Need an experienced WordPress developer to create a custom theme and implement advanced functionality...',
            platform: 'upwork',
            url: 'https://www.upwork.com/jobs/~01234567894',
            date: new Date(Date.now() - 14400000).toISOString(),
            price: '$15-35/hr',
            category: 'web'
        }
    ];
}

// Fetch Khamsat jobs with real data attempts
async function fetchKhamsatJobs() {
    console.log('Fetching real Khamsat jobs...');

    try {
        // Try to fetch real data from Khamsat
        const categories = ['programming', 'design', 'marketing'];
        const randomCategory = categories[Math.floor(Math.random() * categories.length)];

        const proxyServices = [
            'https://api.allorigins.win/get?url=',
            'https://api.codetabs.com/v1/proxy?quest='
        ];

        for (const proxy of proxyServices) {
            try {
                const targetUrl = `https://khamsat.com/${randomCategory}`;
                const proxyUrl = proxy + encodeURIComponent(targetUrl);

                console.log(`Trying Khamsat proxy: ${proxy}`);

                const response = await fetch(proxyUrl, {
                    method: 'GET',
                    headers: {
                        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                    }
                });

                if (response.ok) {
                    const data = await response.text();
                    let html = data;

                    if (proxy.includes('allorigins')) {
                        const jsonData = JSON.parse(data);
                        html = jsonData.contents;
                    }

                    const jobs = parseKhamsatHTML(html);
                    if (jobs && jobs.length > 0) {
                        console.log(`Successfully fetched ${jobs.length} real Khamsat jobs`);
                        return jobs;
                    }
                }
            } catch (proxyError) {
                console.log(`Khamsat proxy ${proxy} failed:`, proxyError.message);
                continue;
            }
        }

        throw new Error('All Khamsat methods failed');

    } catch (error) {
        console.log('Khamsat real data failed, using enhanced mock data');
        return getEnhancedKhamsatJobs();
    }
}

// Parse Khamsat HTML
function parseKhamsatHTML(html) {
    try {
        const parser = new DOMParser();
        const doc = parser.parseFromString(html, 'text/html');

        const jobs = [];

        // Try multiple selectors for Khamsat services
        const selectors = [
            '.service-card',
            '.gig-card',
            '.service-item',
            '.card',
            '.service'
        ];

        let serviceElements = [];
        for (const selector of selectors) {
            serviceElements = doc.querySelectorAll(selector);
            if (serviceElements.length > 0) {
                console.log(`Found ${serviceElements.length} Khamsat services using selector: ${selector}`);
                break;
            }
        }

        serviceElements.forEach((element, index) => {
            if (index >= 5) return;

            try {
                const titleSelectors = [
                    '.service-title a',
                    '.gig-title a',
                    'h3 a',
                    'h4 a',
                    '.title a'
                ];

                let titleElement = null;
                for (const selector of titleSelectors) {
                    titleElement = element.querySelector(selector);
                    if (titleElement) break;
                }

                const descSelectors = [
                    '.service-description',
                    '.gig-description',
                    '.description',
                    'p'
                ];

                let descElement = null;
                for (const selector of descSelectors) {
                    descElement = element.querySelector(selector);
                    if (descElement) break;
                }

                const priceSelectors = [
                    '.service-price',
                    '.gig-price',
                    '.price',
                    '.cost'
                ];

                let priceElement = null;
                for (const selector of priceSelectors) {
                    priceElement = element.querySelector(selector);
                    if (priceElement) break;
                }

                if (titleElement && titleElement.textContent.trim()) {
                    const title = titleElement.textContent.trim();
                    const description = descElement ?
                        descElement.textContent.trim().substring(0, 150) + '...' :
                        'لا يوجد وصف متاح';
                    const price = priceElement ?
                        priceElement.textContent.trim() :
                        'غير محدد';

                    let url = titleElement.href;
                    if (url && !url.startsWith('http')) {
                        url = 'https://khamsat.com' + url;
                    }

                    jobs.push({
                        id: `khamsat_real_${Date.now()}_${index}`,
                        title: title,
                        description: description,
                        platform: 'khamsat',
                        url: url || 'https://khamsat.com',
                        date: new Date().toISOString(),
                        price: price,
                        category: detectJobCategory(title + ' ' + description)
                    });
                }
            } catch (error) {
                console.error('Error parsing Khamsat service element:', error);
            }
        });

        return jobs;
    } catch (error) {
        console.error('Error parsing Khamsat HTML:', error);
        return [];
    }
}

// Enhanced Khamsat mock data
function getEnhancedKhamsatJobs() {
    const currentTime = Date.now();
    return [
        {
            id: `khamsat_enhanced_${currentTime}_1`,
            title: 'تطوير موقع إلكتروني متجاوب بـ React.js',
            description: 'مطلوب مطور ويب محترف لإنشاء موقع إلكتروني متجاوب وحديث باستخدام React.js مع تصميم عصري وسرعة تحميل عالية وتجربة مستخدم ممتازة...',
            platform: 'khamsat',
            url: 'https://khamsat.com/programming/web/react-website',
            date: new Date(currentTime - Math.random() * 3600000).toISOString(),
            price: '$300-600',
            category: 'web'
        },
        {
            id: `khamsat_enhanced_${currentTime}_2`,
            title: 'تصميم تطبيق جوال - UI/UX Design متقدم',
            description: 'نحتاج مصمم UI/UX محترف لتصميم واجهات تطبيق جوال حديث ومبتكر مع تجربة مستخدم استثنائية وتصميم يتماشى مع أحدث الاتجاهات...',
            platform: 'khamsat',
            url: 'https://khamsat.com/design/mobile/ui-ux-app',
            date: new Date(currentTime - Math.random() * 7200000).toISOString(),
            price: '$200-400',
            category: 'design'
        },
        {
            id: `khamsat_enhanced_${currentTime}_3`,
            title: 'برمجة تطبيق Flutter متعدد المنصات',
            description: 'مطلوب مطور Flutter خبير لبرمجة تطبيق جوال متعدد المنصات مع ميزات متقدمة وأداء عالي وتكامل مع APIs خارجية...',
            platform: 'khamsat',
            url: 'https://khamsat.com/programming/mobile/flutter-app',
            date: new Date(currentTime - Math.random() * 10800000).toISOString(),
            price: '$500-1000',
            category: 'mobile'
        },
        {
            id: `khamsat_enhanced_${currentTime}_4`,
            title: 'تحليل البيانات وإنشاء لوحات تحكم تفاعلية',
            description: 'مطلوب محلل بيانات خبير لتحليل البيانات الضخمة وإنشاء لوحات تحكم تفاعلية باستخدام Python وPower BI...',
            platform: 'khamsat',
            url: 'https://khamsat.com/programming/data/analytics-dashboard',
            date: new Date(currentTime - Math.random() * 14400000).toISOString(),
            price: '$400-800',
            category: 'data'
        }
    ];
}

// Fetch Wuzzuf jobs with real data attempts
async function fetchWuzzufJobs() {
    console.log('Fetching real Wuzzuf jobs...');

    try {
        // Try to fetch real data from Wuzzuf
        const searchTerms = ['developer', 'programmer', 'software', 'web', 'mobile'];
        const randomTerm = searchTerms[Math.floor(Math.random() * searchTerms.length)];

        const proxyServices = [
            'https://api.allorigins.win/get?url=',
            'https://api.codetabs.com/v1/proxy?quest='
        ];

        for (const proxy of proxyServices) {
            try {
                const targetUrl = `https://wuzzuf.net/search/jobs/?q=${encodeURIComponent(randomTerm)}&a=hpb`;
                const proxyUrl = proxy + encodeURIComponent(targetUrl);

                console.log(`Trying Wuzzuf proxy: ${proxy}`);

                const response = await fetch(proxyUrl, {
                    method: 'GET',
                    headers: {
                        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                    }
                });

                if (response.ok) {
                    const data = await response.text();
                    let html = data;

                    if (proxy.includes('allorigins')) {
                        const jsonData = JSON.parse(data);
                        html = jsonData.contents;
                    }

                    const jobs = parseWuzzufHTML(html);
                    if (jobs && jobs.length > 0) {
                        console.log(`Successfully fetched ${jobs.length} real Wuzzuf jobs`);
                        return jobs;
                    }
                }
            } catch (proxyError) {
                console.log(`Wuzzuf proxy ${proxy} failed:`, proxyError.message);
                continue;
            }
        }

        // Try RSS if available
        try {
            const rssUrl = `https://wuzzuf.net/search/jobs/rss?q=${encodeURIComponent(randomTerm)}`;
            const response = await fetch(`https://api.rss2json.com/v1/api.json?rss_url=${encodeURIComponent(rssUrl)}`);

            if (response.ok) {
                const rssData = await response.json();
                if (rssData.items && rssData.items.length > 0) {
                    return parseWuzzufRSS(rssData.items);
                }
            }
        } catch (rssError) {
            console.log('Wuzzuf RSS method failed:', rssError.message);
        }

        throw new Error('All Wuzzuf methods failed');

    } catch (error) {
        console.log('Wuzzuf real data failed, using enhanced mock data');
        return getEnhancedWuzzufJobs();
    }
}

// Parse Wuzzuf HTML
function parseWuzzufHTML(html) {
    try {
        const parser = new DOMParser();
        const doc = parser.parseFromString(html, 'text/html');

        const jobs = [];

        // Try multiple selectors for Wuzzuf jobs
        const selectors = [
            '.css-1gatmva',
            '.job-card',
            '.search-result',
            '.job-item',
            '[data-cy="job-card"]'
        ];

        let jobElements = [];
        for (const selector of selectors) {
            jobElements = doc.querySelectorAll(selector);
            if (jobElements.length > 0) {
                console.log(`Found ${jobElements.length} Wuzzuf jobs using selector: ${selector}`);
                break;
            }
        }

        jobElements.forEach((element, index) => {
            if (index >= 5) return;

            try {
                const titleSelectors = [
                    'h2 a',
                    '.job-title a',
                    'h3 a',
                    '.css-m604qf a',
                    '[data-cy="job-title"] a'
                ];

                let titleElement = null;
                for (const selector of titleSelectors) {
                    titleElement = element.querySelector(selector);
                    if (titleElement) break;
                }

                const descSelectors = [
                    '.css-y4udm8',
                    '.job-description',
                    '.description',
                    'p',
                    '.css-1uobp1k'
                ];

                let descElement = null;
                for (const selector of descSelectors) {
                    descElement = element.querySelector(selector);
                    if (descElement) break;
                }

                const companySelectors = [
                    '.css-d7j1kk',
                    '.company-name',
                    '.css-17s97q8',
                    '[data-cy="company-name"]'
                ];

                let companyElement = null;
                for (const selector of companySelectors) {
                    companyElement = element.querySelector(selector);
                    if (companyElement) break;
                }

                const salarySelectors = [
                    '.css-4xky9y',
                    '.salary',
                    '.css-rcl8e5',
                    '[data-cy="salary"]'
                ];

                let salaryElement = null;
                for (const selector of salarySelectors) {
                    salaryElement = element.querySelector(selector);
                    if (salaryElement && salaryElement.textContent.includes('EGP')) break;
                }

                if (titleElement && titleElement.textContent.trim()) {
                    const title = titleElement.textContent.trim();
                    const description = descElement ?
                        descElement.textContent.trim().substring(0, 150) + '...' :
                        'لا يوجد وصف متاح';
                    const company = companyElement ? companyElement.textContent.trim() : '';
                    const salary = salaryElement ?
                        salaryElement.textContent.trim() :
                        'غير محدد';

                    let url = titleElement.href;
                    if (url && !url.startsWith('http')) {
                        url = 'https://wuzzuf.net' + url;
                    }

                    jobs.push({
                        id: `wuzzuf_real_${Date.now()}_${index}`,
                        title: title,
                        description: description + (company ? ` - ${company}` : ''),
                        platform: 'wuzzuf',
                        url: url || 'https://wuzzuf.net',
                        date: new Date().toISOString(),
                        price: salary,
                        category: detectJobCategory(title + ' ' + description)
                    });
                }
            } catch (error) {
                console.error('Error parsing Wuzzuf job element:', error);
            }
        });

        return jobs;
    } catch (error) {
        console.error('Error parsing Wuzzuf HTML:', error);
        return [];
    }
}

// Parse Wuzzuf RSS feed
function parseWuzzufRSS(items) {
    const jobs = [];

    items.slice(0, 5).forEach((item, index) => {
        try {
            jobs.push({
                id: `wuzzuf_rss_${Date.now()}_${index}`,
                title: item.title || 'عنوان غير متاح',
                description: (item.description || item.content || 'لا يوجد وصف متاح').substring(0, 150) + '...',
                platform: 'wuzzuf',
                url: item.link || 'https://wuzzuf.net',
                date: item.pubDate || new Date().toISOString(),
                price: extractSalaryFromText(item.description || ''),
                category: detectJobCategory((item.title || '') + ' ' + (item.description || ''))
            });
        } catch (error) {
            console.error('Error parsing Wuzzuf RSS item:', error);
        }
    });

    return jobs;
}

// Extract salary information from text (EGP format)
function extractSalaryFromText(text) {
    const salaryPatterns = [
        /\d+(?:,\d{3})*\s*-\s*\d+(?:,\d{3})*\s*EGP/gi,
        /\d+(?:,\d{3})*\s*EGP/gi,
        /Salary:\s*\d+(?:,\d{3})*\s*EGP/gi,
        /راتب:\s*\d+(?:,\d{3})*\s*جنيه/gi
    ];

    for (const pattern of salaryPatterns) {
        const match = text.match(pattern);
        if (match) {
            return match[0];
        }
    }

    return 'غير محدد';
}

// Enhanced Wuzzuf mock data
function getEnhancedWuzzufJobs() {
    const currentTime = Date.now();
    return [
        {
            id: `wuzzuf_enhanced_${currentTime}_1`,
            title: 'Senior Frontend Developer - React.js & TypeScript',
            description: 'We are looking for a skilled Senior Frontend Developer to join our team and work on cutting-edge web applications using React.js, TypeScript, and modern development practices...',
            platform: 'wuzzuf',
            url: 'https://wuzzuf.net/jobs/p/senior-frontend-developer-react',
            date: new Date(currentTime - Math.random() * 3600000).toISOString(),
            price: '12000-20000 EGP',
            category: 'web'
        },
        {
            id: `wuzzuf_enhanced_${currentTime}_2`,
            title: 'Data Scientist - Machine Learning & AI',
            description: 'Join our data team as a Data Scientist specializing in machine learning, AI solutions, data visualization, and advanced analytics using Python and TensorFlow...',
            platform: 'wuzzuf',
            url: 'https://wuzzuf.net/jobs/p/data-scientist-ml-ai',
            date: new Date(currentTime - Math.random() * 7200000).toISOString(),
            price: '15000-25000 EGP',
            category: 'data'
        },
        {
            id: `wuzzuf_enhanced_${currentTime}_3`,
            title: 'Mobile App Developer - Flutter & Native',
            description: 'Seeking an experienced mobile app developer to create cross-platform applications using Flutter and native development for Android and iOS platforms...',
            platform: 'wuzzuf',
            url: 'https://wuzzuf.net/jobs/p/mobile-developer-flutter',
            date: new Date(currentTime - Math.random() * 10800000).toISOString(),
            price: '10000-18000 EGP',
            category: 'mobile'
        },
        {
            id: `wuzzuf_enhanced_${currentTime}_4`,
            title: 'Full Stack Developer - MERN Stack',
            description: 'Looking for a Full Stack Developer with expertise in MongoDB, Express.js, React.js, and Node.js to build scalable web applications...',
            platform: 'wuzzuf',
            url: 'https://wuzzuf.net/jobs/p/fullstack-developer-mern',
            date: new Date(currentTime - Math.random() * 14400000).toISOString(),
            price: '8000-16000 EGP',
            category: 'web'
        },
        {
            id: `wuzzuf_enhanced_${currentTime}_5`,
            title: 'UI/UX Designer - Digital Products',
            description: 'We need a creative UI/UX Designer to design user-friendly interfaces for our digital products using Figma, Adobe XD, and modern design principles...',
            platform: 'wuzzuf',
            url: 'https://wuzzuf.net/jobs/p/ui-ux-designer-digital',
            date: new Date(currentTime - Math.random() * 18000000).toISOString(),
            price: '6000-12000 EGP',
            category: 'design'
        }
    ];
}

// Auto-refresh functionality
chrome.alarms.onAlarm.addListener((alarm) => {
    if (alarm.name === 'autoRefresh') {
        console.log('Auto-refreshing jobs...');
        // Trigger job refresh
        chrome.runtime.sendMessage({ action: 'autoRefresh' });
    }
});

// Set up auto-refresh alarm
chrome.storage.local.get(['settings'], (result) => {
    if (result.settings && result.settings.autoRefresh) {
        chrome.alarms.create('autoRefresh', {
            delayInMinutes: result.settings.refreshInterval,
            periodInMinutes: result.settings.refreshInterval
        });
    }
});
