// Background script for handling CORS and API requests
console.log('Background script loaded');

// Handle extension installation
chrome.runtime.onInstalled.addListener((details) => {
    console.log('Extension installed:', details.reason);
    
    if (details.reason === 'install') {
        // Set default settings
        chrome.storage.local.set({
            settings: {
                autoRefresh: true,
                refreshInterval: 30, // minutes
                notifications: true,
                maxJobs: 15
            }
        });
    }
});

// Handle messages from popup
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    console.log('Background received message:', request);
    
    switch (request.action) {
        case 'fetchJobs':
            handleFetchJobs(request.platform)
                .then(jobs => sendResponse({ success: true, data: jobs }))
                .catch(error => sendResponse({ success: false, error: error.message }));
            return true; // Keep message channel open for async response
            
        case 'openTab':
            chrome.tabs.create({ url: request.url });
            sendResponse({ success: true });
            break;
            
        default:
            sendResponse({ success: false, error: 'Unknown action' });
    }
});

// Fetch jobs from different platforms
async function handleFetchJobs(platform) {
    console.log(`Fetching jobs from ${platform}`);
    
    try {
        switch (platform) {
            case 'upwork':
                return await fetchUpworkJobs();
            case 'khamsat':
                return await fetchKhamsatJobs();
            case 'wuzzuf':
                return await fetchWuzzufJobs();
            default:
                throw new Error('Unknown platform');
        }
    } catch (error) {
        console.error(`Error fetching ${platform} jobs:`, error);
        throw error;
    }
}

// Fetch Upwork jobs using real data methods
async function fetchUpworkJobs() {
    console.log('Fetching real Upwork jobs...');

    try {
        // Method 1: Try direct fetch with different approaches
        const searchQueries = [
            'web development',
            'react developer',
            'javascript developer',
            'frontend developer',
            'fullstack developer'
        ];

        const randomQuery = searchQueries[Math.floor(Math.random() * searchQueries.length)];

        // Try multiple proxy services and real APIs
        const proxyServices = [
            'https://api.allorigins.win/get?url=',
            'https://cors-anywhere.herokuapp.com/',
            'https://api.codetabs.com/v1/proxy?quest=',
            'https://thingproxy.freeboard.io/fetch/',
            'https://yacdn.org/proxy/',
            'https://api.codetabs.com/v1/proxy/?quest='
        ];

        for (const proxy of proxyServices) {
            try {
                const targetUrl = `https://www.upwork.com/nx/search/jobs/?q=${encodeURIComponent(randomQuery)}&sort=recency`;
                const proxyUrl = proxy + encodeURIComponent(targetUrl);

                console.log(`Trying proxy: ${proxy}`);

                const response = await fetch(proxyUrl, {
                    method: 'GET',
                    headers: {
                        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                    }
                });

                if (response.ok) {
                    const data = await response.text();
                    let html = data;

                    // Handle different proxy response formats
                    if (proxy.includes('allorigins')) {
                        const jsonData = JSON.parse(data);
                        html = jsonData.contents;
                    }

                    const jobs = parseUpworkHTML(html);
                    if (jobs && jobs.length > 0) {
                        console.log(`Successfully fetched ${jobs.length} real Upwork jobs`);
                        return jobs;
                    }
                }
            } catch (proxyError) {
                console.log(`Proxy ${proxy} failed:`, proxyError.message);
                continue;
            }
        }

        // Method 2: Try RSS feeds with multiple services
        const rssServices = [
            'https://api.rss2json.com/v1/api.json?rss_url=',
            'https://rss2json.com/api.json?rss_url=',
            'https://api.allorigins.win/get?url='
        ];

        for (const rssService of rssServices) {
            try {
                const rssUrl = `https://www.upwork.com/ab/feed/jobs/rss?q=${encodeURIComponent(randomQuery)}&sort=recency`;
                const response = await fetch(rssService + encodeURIComponent(rssUrl));

                if (response.ok) {
                    const data = await response.text();
                    let rssData;

                    if (rssService.includes('allorigins')) {
                        const jsonData = JSON.parse(data);
                        rssData = parseRSSXML(jsonData.contents);
                    } else {
                        rssData = JSON.parse(data);
                    }

                    if (rssData.items && rssData.items.length > 0) {
                        console.log(`✅ RSS success with ${rssService}`);
                        return parseUpworkRSS(rssData.items);
                    }
                }
            } catch (rssError) {
                console.log(`RSS service ${rssService} failed:`, rssError.message);
                continue;
            }
        }

        // Method 3: Try Upwork API endpoints (real APIs)
        const upworkApiEndpoints = [
            `https://www.upwork.com/api/profiles/v1/search/jobs?q=${encodeURIComponent(randomQuery)}`,
            `https://api.upwork.com/api/profiles/v1/search/jobs?query=${encodeURIComponent(randomQuery)}`,
            `https://www.upwork.com/ab/jobs/api/search?q=${encodeURIComponent(randomQuery)}&sort=recency`,
            `https://www.upwork.com/freelance-jobs/api?search=${encodeURIComponent(randomQuery)}`
        ];

        for (const apiEndpoint of upworkApiEndpoints) {
            try {
                console.log(`Trying Upwork API: ${apiEndpoint}`);
                const response = await fetch(apiEndpoint, {
                    headers: {
                        'Accept': 'application/json',
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    const jobs = parseUpworkAPIResponse(data);
                    if (jobs.length > 0) {
                        console.log(`✅ Upwork API success: ${jobs.length} jobs`);
                        return jobs;
                    }
                }
            } catch (apiError) {
                console.log(`Upwork API ${apiEndpoint} failed:`, apiError.message);
                continue;
            }
        }

        // Method 4: Try alternative Upwork endpoints
        const alternativeEndpoints = [
            'https://www.upwork.com/freelance-jobs/',
            'https://www.upwork.com/jobs/search/?q=developer',
            'https://www.upwork.com/ab/jobs/search/?q=programming'
        ];

        for (const endpoint of alternativeEndpoints) {
            try {
                const response = await fetch(`https://api.allorigins.win/get?url=${encodeURIComponent(endpoint)}`);
                if (response.ok) {
                    const data = await response.json();
                    const jobs = parseJobsWithRegex(data.contents, 'upwork');
                    if (jobs.length > 0) {
                        console.log(`✅ Alternative endpoint success: ${endpoint}`);
                        return jobs;
                    }
                }
            } catch (error) {
                console.log(`Alternative endpoint failed: ${endpoint}`);
                continue;
            }
        }

        throw new Error('All methods failed');

    } catch (error) {
        console.log('❌ All real data methods failed for Upwork');
        return []; // Return empty array instead of mock data
    }
}

// Parse Upwork HTML using regex (service worker compatible)
function parseUpworkHTML(html) {
    try {
        return parseJobsWithRegex(html, 'upwork');
    } catch (error) {
        console.error('Error parsing Upwork HTML:', error);
        return [];
    }
}

// Parse Upwork RSS feed
function parseUpworkRSS(items) {
    const jobs = [];

    items.slice(0, 5).forEach((item, index) => {
        try {
            jobs.push({
                id: `upwork_rss_${Date.now()}_${index}`,
                title: item.title || 'عنوان غير متاح',
                description: (item.description || item.content || 'لا يوجد وصف متاح').substring(0, 150) + '...',
                platform: 'upwork',
                url: item.link || 'https://www.upwork.com',
                date: item.pubDate || new Date().toISOString(),
                price: extractPriceFromText(item.description || ''),
                category: detectJobCategory((item.title || '') + ' ' + (item.description || ''))
            });
        } catch (error) {
            console.error('Error parsing RSS item:', error);
        }
    });

    return jobs;
}

// Extract price information from text
function extractPriceFromText(text) {
    const pricePatterns = [
        /\$\d+(?:-\$?\d+)?(?:\/hr|\/hour)?/gi,
        /\$\d+(?:\.\d{2})?(?:\s*-\s*\$?\d+(?:\.\d{2})?)?/gi,
        /Budget:\s*\$\d+/gi,
        /Hourly:\s*\$\d+/gi
    ];

    for (const pattern of pricePatterns) {
        const match = text.match(pattern);
        if (match) {
            return match[0];
        }
    }

    return 'غير محدد';
}

// Detect job category from text
function detectJobCategory(text) {
    const lowerText = text.toLowerCase();

    const categories = {
        'web': ['react', 'vue', 'angular', 'frontend', 'web', 'html', 'css', 'javascript', 'node', 'express'],
        'mobile': ['mobile', 'android', 'ios', 'flutter', 'react native', 'swift', 'kotlin', 'app'],
        'data': ['data', 'python', 'machine learning', 'ai', 'analytics', 'sql', 'pandas', 'tensorflow'],
        'design': ['design', 'ui', 'ux', 'figma', 'photoshop', 'graphic', 'logo', 'branding']
    };

    for (const [category, keywords] of Object.entries(categories)) {
        if (keywords.some(keyword => lowerText.includes(keyword))) {
            return category;
        }
    }

    return 'web'; // Default category
}

// Parse jobs using regex (service worker compatible)
function parseJobsWithRegex(html, platform) {
    const jobs = [];

    try {
        // Remove HTML comments and scripts
        const cleanHtml = html.replace(/<!--[\s\S]*?-->/g, '').replace(/<script[\s\S]*?<\/script>/gi, '');

        // Platform-specific regex patterns
        let patterns = {};

        switch (platform) {
            case 'upwork':
                patterns = {
                    jobContainer: /<article[^>]*data-test="JobTile"[^>]*>([\s\S]*?)<\/article>/gi,
                    title: /<h[2-4][^>]*data-test="JobTileTitle"[^>]*>[\s\S]*?<a[^>]*>(.*?)<\/a>/gi,
                    description: /<p[^>]*data-test="JobTileDescription"[^>]*>(.*?)<\/p>/gi,
                    price: /<span[^>]*data-test="JobTileBudget"[^>]*>(.*?)<\/span>/gi,
                    url: /<a[^>]*href="([^"]*)"[^>]*data-test="UpLink"/gi
                };
                break;

            case 'khamsat':
                patterns = {
                    jobContainer: /<div[^>]*class="[^"]*service[^"]*"[^>]*>([\s\S]*?)<\/div>/gi,
                    title: /<h[2-4][^>]*>[\s\S]*?<a[^>]*>(.*?)<\/a>/gi,
                    description: /<p[^>]*class="[^"]*description[^"]*"[^>]*>(.*?)<\/p>/gi,
                    price: /<span[^>]*class="[^"]*price[^"]*"[^>]*>(.*?)<\/span>/gi,
                    url: /<a[^>]*href="([^"]*)"[^>]*>/gi
                };
                break;

            case 'wuzzuf':
                patterns = {
                    jobContainer: /<div[^>]*class="[^"]*job[^"]*"[^>]*>([\s\S]*?)<\/div>/gi,
                    title: /<h[2-4][^>]*>[\s\S]*?<a[^>]*>(.*?)<\/a>/gi,
                    description: /<p[^>]*>(.*?)<\/p>/gi,
                    price: /<span[^>]*>(.*?EGP.*?)<\/span>/gi,
                    url: /<a[^>]*href="([^"]*)"[^>]*>/gi
                };
                break;
        }

        // Extract job containers
        let match;
        let jobIndex = 0;

        while ((match = patterns.jobContainer.exec(cleanHtml)) !== null && jobIndex < 5) {
            const jobHtml = match[1];

            // Extract title
            const titleMatch = patterns.title.exec(jobHtml);
            const title = titleMatch ? stripHtmlTags(titleMatch[1]).trim() : null;

            if (!title) continue;

            // Extract description
            patterns.description.lastIndex = 0;
            const descMatch = patterns.description.exec(jobHtml);
            const description = descMatch ?
                stripHtmlTags(descMatch[1]).trim().substring(0, 150) + '...' :
                'لا يوجد وصف متاح';

            // Extract price
            patterns.price.lastIndex = 0;
            const priceMatch = patterns.price.exec(jobHtml);
            const price = priceMatch ? stripHtmlTags(priceMatch[1]).trim() : 'غير محدد';

            // Extract URL
            patterns.url.lastIndex = 0;
            const urlMatch = patterns.url.exec(jobHtml);
            let url = urlMatch ? urlMatch[1] : '';

            // Fix relative URLs
            if (url && !url.startsWith('http')) {
                const baseUrls = {
                    'upwork': 'https://www.upwork.com',
                    'khamsat': 'https://khamsat.com',
                    'wuzzuf': 'https://wuzzuf.net'
                };
                url = baseUrls[platform] + (url.startsWith('/') ? url : '/' + url);
            }

            jobs.push({
                id: `${platform}_regex_${Date.now()}_${jobIndex}`,
                title: title,
                description: description,
                platform: platform,
                url: url || getDefaultUrl(platform),
                date: new Date().toISOString(),
                price: price,
                category: detectJobCategory(title + ' ' + description)
            });

            jobIndex++;
        }

        console.log(`Regex parsing found ${jobs.length} jobs from ${platform}`);
        return jobs;

    } catch (error) {
        console.error(`Error in regex parsing for ${platform}:`, error);
        return [];
    }
}

// Strip HTML tags from text
function stripHtmlTags(html) {
    return html.replace(/<[^>]*>/g, '').replace(/&[^;]+;/g, ' ').trim();
}

// Get default URL for platform
function getDefaultUrl(platform) {
    const urls = {
        'upwork': 'https://www.upwork.com',
        'khamsat': 'https://khamsat.com',
        'wuzzuf': 'https://wuzzuf.net'
    };
    return urls[platform] || '#';
}

// Parse RSS XML content
function parseRSSXML(xmlContent) {
    try {
        const items = [];

        // Extract items using regex
        const itemMatches = xmlContent.match(/<item[^>]*>([\s\S]*?)<\/item>/gi);

        if (itemMatches) {
            itemMatches.forEach(itemXml => {
                const title = extractXMLTag(itemXml, 'title');
                const description = extractXMLTag(itemXml, 'description');
                const link = extractXMLTag(itemXml, 'link');
                const pubDate = extractXMLTag(itemXml, 'pubDate');

                if (title) {
                    items.push({
                        title: title,
                        description: description,
                        link: link,
                        pubDate: pubDate
                    });
                }
            });
        }

        return { items: items };
    } catch (error) {
        console.error('Error parsing RSS XML:', error);
        return { items: [] };
    }
}

// Extract content from XML tag
function extractXMLTag(xml, tagName) {
    const regex = new RegExp(`<${tagName}[^>]*>([\\s\\S]*?)<\\/${tagName}>`, 'i');
    const match = xml.match(regex);
    return match ? match[1].replace(/<!\[CDATA\[(.*?)\]\]>/g, '$1').trim() : '';
}

// Parse Upwork API response
function parseUpworkAPIResponse(data) {
    const jobs = [];

    try {
        // Try different possible API response structures
        let jobsArray = [];

        if (data.jobs) {
            jobsArray = data.jobs;
        } else if (data.results) {
            jobsArray = data.results;
        } else if (data.data && data.data.jobs) {
            jobsArray = data.data.jobs;
        } else if (data.searchResults) {
            jobsArray = data.searchResults;
        } else if (Array.isArray(data)) {
            jobsArray = data;
        }

        jobsArray.slice(0, 5).forEach((job, index) => {
            try {
                const title = job.title || job.jobTitle || job.name || 'عنوان غير متاح';
                const description = job.description || job.jobDescription || job.snippet || job.summary || 'لا يوجد وصف متاح';
                const budget = job.budget || job.hourlyRate || job.price || job.compensation || 'غير محدد';
                const url = job.url || job.jobUrl || job.link || `https://www.upwork.com/jobs/${job.id || index}`;
                const client = job.client || job.clientName || job.company || '';
                const skills = job.skills || job.requiredSkills || [];

                jobs.push({
                    id: `upwork_api_${Date.now()}_${index}`,
                    title: title,
                    description: (description + (client ? ` - Client: ${client}` : '') + (skills.length ? ` - Skills: ${skills.slice(0,3).join(', ')}` : '')).substring(0, 150) + '...',
                    platform: 'upwork',
                    url: url,
                    date: job.dateCreated || job.postedOn || job.publishedOn || new Date().toISOString(),
                    price: budget,
                    category: detectJobCategory(title + ' ' + description + ' ' + skills.join(' '))
                });
            } catch (error) {
                console.error('Error parsing Upwork API job:', error);
            }
        });

        return jobs;
    } catch (error) {
        console.error('Error parsing Upwork API response:', error);
        return [];
    }
}

// No mock data - only real data

// Fetch Khamsat jobs with real data attempts
async function fetchKhamsatJobs() {
    console.log('Fetching real Khamsat jobs...');

    try {
        // Try to fetch real data from Khamsat
        const categories = ['programming', 'design', 'marketing'];
        const randomCategory = categories[Math.floor(Math.random() * categories.length)];

        const proxyServices = [
            'https://api.allorigins.win/get?url=',
            'https://api.codetabs.com/v1/proxy?quest='
        ];

        for (const proxy of proxyServices) {
            try {
                const targetUrl = `https://khamsat.com/${randomCategory}`;
                const proxyUrl = proxy + encodeURIComponent(targetUrl);

                console.log(`Trying Khamsat proxy: ${proxy}`);

                const response = await fetch(proxyUrl, {
                    method: 'GET',
                    headers: {
                        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                    }
                });

                if (response.ok) {
                    const data = await response.text();
                    let html = data;

                    if (proxy.includes('allorigins')) {
                        const jsonData = JSON.parse(data);
                        html = jsonData.contents;
                    }

                    const jobs = parseKhamsatHTML(html);
                    if (jobs && jobs.length > 0) {
                        console.log(`Successfully fetched ${jobs.length} real Khamsat jobs`);
                        return jobs;
                    }
                }
            } catch (proxyError) {
                console.log(`Khamsat proxy ${proxy} failed:`, proxyError.message);
                continue;
            }
        }

        throw new Error('All Khamsat methods failed');

    } catch (error) {
        console.log('❌ All real data methods failed for Khamsat');
        return []; // Return empty array instead of mock data
    }
}

// Parse Khamsat HTML using regex (service worker compatible)
function parseKhamsatHTML(html) {
    try {
        return parseJobsWithRegex(html, 'khamsat');
    } catch (error) {
        console.error('Error parsing Khamsat HTML:', error);
        return [];
    }
}

// No mock data - only real data

// Fetch Wuzzuf jobs with real data attempts
async function fetchWuzzufJobs() {
    console.log('Fetching real Wuzzuf jobs...');

    try {
        // Try to fetch real data from Wuzzuf
        const searchTerms = ['developer', 'programmer', 'software', 'web', 'mobile'];
        const randomTerm = searchTerms[Math.floor(Math.random() * searchTerms.length)];

        // Method 1: Try Wuzzuf API endpoints (real APIs)
        const wuzzufApiEndpoints = [
            `https://wuzzuf.net/api/search/jobs?q=${encodeURIComponent(randomTerm)}&start=0&rows=10`,
            `https://wuzzuf.net/search/jobs/api?query=${encodeURIComponent(randomTerm)}&limit=10`,
            `https://api.wuzzuf.net/v1/jobs?search=${encodeURIComponent(randomTerm)}&count=10`,
            `https://wuzzuf.net/jobs/search?q=${encodeURIComponent(randomTerm)}&format=json`,
            `https://www.wuzzuf.net/api/jobs/search?keyword=${encodeURIComponent(randomTerm)}`
        ];

        for (const apiEndpoint of wuzzufApiEndpoints) {
            try {
                console.log(`Trying Wuzzuf API: ${apiEndpoint}`);
                const response = await fetch(apiEndpoint, {
                    headers: {
                        'Accept': 'application/json',
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    const jobs = parseWuzzufAPIResponse(data);
                    if (jobs.length > 0) {
                        console.log(`✅ Wuzzuf API success: ${jobs.length} jobs`);
                        return jobs;
                    }
                }
            } catch (apiError) {
                console.log(`Wuzzuf API ${apiEndpoint} failed:`, apiError.message);
                continue;
            }
        }

        const proxyServices = [
            'https://api.allorigins.win/get?url=',
            'https://api.codetabs.com/v1/proxy?quest=',
            'https://thingproxy.freeboard.io/fetch/',
            'https://yacdn.org/proxy/',
            'https://cors-anywhere.herokuapp.com/'
        ];

        for (const proxy of proxyServices) {
            try {
                const targetUrl = `https://wuzzuf.net/search/jobs/?q=${encodeURIComponent(randomTerm)}&a=hpb`;
                const proxyUrl = proxy + encodeURIComponent(targetUrl);

                console.log(`Trying Wuzzuf proxy: ${proxy}`);

                const response = await fetch(proxyUrl, {
                    method: 'GET',
                    headers: {
                        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                    }
                });

                if (response.ok) {
                    const data = await response.text();
                    let html = data;

                    if (proxy.includes('allorigins')) {
                        const jsonData = JSON.parse(data);
                        html = jsonData.contents;
                    }

                    const jobs = parseWuzzufHTML(html);
                    if (jobs && jobs.length > 0) {
                        console.log(`Successfully fetched ${jobs.length} real Wuzzuf jobs`);
                        return jobs;
                    }
                }
            } catch (proxyError) {
                console.log(`Wuzzuf proxy ${proxy} failed:`, proxyError.message);
                continue;
            }
        }

        // Try RSS if available
        try {
            const rssUrl = `https://wuzzuf.net/search/jobs/rss?q=${encodeURIComponent(randomTerm)}`;
            const response = await fetch(`https://api.rss2json.com/v1/api.json?rss_url=${encodeURIComponent(rssUrl)}`);

            if (response.ok) {
                const rssData = await response.json();
                if (rssData.items && rssData.items.length > 0) {
                    return parseWuzzufRSS(rssData.items);
                }
            }
        } catch (rssError) {
            console.log('Wuzzuf RSS method failed:', rssError.message);
        }

        throw new Error('All Wuzzuf methods failed');

    } catch (error) {
        console.log('❌ All real data methods failed for Wuzzuf');
        return []; // Return empty array instead of mock data
    }
}

// Parse Wuzzuf HTML using regex (service worker compatible)
function parseWuzzufHTML(html) {
    try {
        return parseJobsWithRegex(html, 'wuzzuf');
    } catch (error) {
        console.error('Error parsing Wuzzuf HTML:', error);
        return [];
    }
}

// Parse Wuzzuf RSS feed
function parseWuzzufRSS(items) {
    const jobs = [];

    items.slice(0, 5).forEach((item, index) => {
        try {
            jobs.push({
                id: `wuzzuf_rss_${Date.now()}_${index}`,
                title: item.title || 'عنوان غير متاح',
                description: (item.description || item.content || 'لا يوجد وصف متاح').substring(0, 150) + '...',
                platform: 'wuzzuf',
                url: item.link || 'https://wuzzuf.net',
                date: item.pubDate || new Date().toISOString(),
                price: extractSalaryFromText(item.description || ''),
                category: detectJobCategory((item.title || '') + ' ' + (item.description || ''))
            });
        } catch (error) {
            console.error('Error parsing Wuzzuf RSS item:', error);
        }
    });

    return jobs;
}

// Extract salary information from text (EGP format)
function extractSalaryFromText(text) {
    const salaryPatterns = [
        /\d+(?:,\d{3})*\s*-\s*\d+(?:,\d{3})*\s*EGP/gi,
        /\d+(?:,\d{3})*\s*EGP/gi,
        /Salary:\s*\d+(?:,\d{3})*\s*EGP/gi,
        /راتب:\s*\d+(?:,\d{3})*\s*جنيه/gi
    ];

    for (const pattern of salaryPatterns) {
        const match = text.match(pattern);
        if (match) {
            return match[0];
        }
    }

    return 'غير محدد';
}

// Parse Wuzzuf API response
function parseWuzzufAPIResponse(data) {
    const jobs = [];

    try {
        // Try different possible API response structures
        let jobsArray = [];

        if (data.jobs) {
            jobsArray = data.jobs;
        } else if (data.data && data.data.jobs) {
            jobsArray = data.data.jobs;
        } else if (data.results) {
            jobsArray = data.results;
        } else if (Array.isArray(data)) {
            jobsArray = data;
        }

        jobsArray.slice(0, 5).forEach((job, index) => {
            try {
                const title = job.title || job.job_title || job.name || 'عنوان غير متاح';
                const description = job.description || job.job_description || job.summary || 'لا يوجد وصف متاح';
                const company = job.company || job.company_name || job.employer || '';
                const salary = job.salary || job.salary_range || job.compensation || 'غير محدد';
                const location = job.location || job.city || job.area || '';
                const url = job.url || job.job_url || job.link || `https://wuzzuf.net/jobs/${job.id || index}`;

                jobs.push({
                    id: `wuzzuf_api_${Date.now()}_${index}`,
                    title: title,
                    description: (description + (company ? ` - ${company}` : '') + (location ? ` - ${location}` : '')).substring(0, 150) + '...',
                    platform: 'wuzzuf',
                    url: url,
                    date: job.created_at || job.posted_date || new Date().toISOString(),
                    price: salary,
                    category: detectJobCategory(title + ' ' + description)
                });
            } catch (error) {
                console.error('Error parsing Wuzzuf API job:', error);
            }
        });

        return jobs;
    } catch (error) {
        console.error('Error parsing Wuzzuf API response:', error);
        return [];
    }
}

// No mock data - only real data

// Auto-refresh functionality
chrome.alarms.onAlarm.addListener((alarm) => {
    if (alarm.name === 'autoRefresh') {
        console.log('Auto-refreshing jobs...');
        // Trigger job refresh
        chrome.runtime.sendMessage({ action: 'autoRefresh' });
    }
});

// Set up auto-refresh alarm
chrome.storage.local.get(['settings'], (result) => {
    if (result.settings && result.settings.autoRefresh) {
        chrome.alarms.create('autoRefresh', {
            delayInMinutes: result.settings.refreshInterval,
            periodInMinutes: result.settings.refreshInterval
        });
    }
});
