<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار إضافة فرص العمل التقنية</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            direction: rtl;
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .content {
            padding: 30px;
        }

        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #eee;
            border-radius: 8px;
            background: #f9f9f9;
        }

        .test-section h2 {
            color: #2c3e50;
            margin-bottom: 15px;
        }

        .test-button {
            background: #4facfe;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .test-button:hover {
            background: #357abd;
            transform: translateY(-2px);
        }

        .result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 6px;
            background: #e9ecef;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 200px;
            overflow-y: auto;
        }

        .success {
            background: #d4edda;
            color: #155724;
        }

        .error {
            background: #f8d7da;
            color: #721c24;
        }

        .job-card {
            background: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .job-title {
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 8px;
        }

        .job-platform {
            display: inline-block;
            background: #4facfe;
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 10px;
            margin-bottom: 8px;
        }

        .instructions {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 اختبار إضافة فرص العمل التقنية</h1>
            <p>صفحة اختبار لتجربة وظائف الإضافة</p>
        </div>

        <div class="content">
            <div class="instructions">
                <h3>📋 تعليمات الاختبار:</h3>
                <ol>
                    <li>تأكد من تثبيت الإضافة في Chrome</li>
                    <li>افتح Developer Tools (F12) لمراقبة الأخطاء</li>
                    <li>اضغط على الأزرار أدناه لاختبار الوظائف المختلفة</li>
                    <li>تحقق من ظهور أيقونة الإضافة في شريط الأدوات</li>
                </ol>
            </div>

            <!-- اختبار التخزين المحلي -->
            <div class="test-section">
                <h2>💾 اختبار التخزين المحلي</h2>
                <button class="test-button" id="testStorageBtn">اختبار حفظ البيانات</button>
                <button class="test-button" id="testLoadStorageBtn">اختبار تحميل البيانات</button>
                <button class="test-button" id="clearStorageBtn">مسح البيانات</button>
                <div id="storageResult" class="result"></div>
            </div>

            <!-- اختبار جلب الوظائف -->
            <div class="test-section">
                <h2>🔍 اختبار جلب الوظائف</h2>
                <button class="test-button" id="testFetchJobsBtn">جلب وظائف تجريبية</button>
                <button class="test-button" id="testUpworkJobsBtn">اختبار Upwork</button>
                <button class="test-button" id="testKhamsatJobsBtn">اختبار خمسات</button>
                <button class="test-button" id="testWuzzufJobsBtn">اختبار وظف</button>
                <div id="jobsResult" class="result"></div>
            </div>

            <!-- اختبار الفلاتر -->
            <div class="test-section">
                <h2>🔧 اختبار الفلاتر</h2>
                <button class="test-button" id="testFiltersBtn">اختبار فلترة الوظائف</button>
                <button class="test-button" id="testCategoriesBtn">اختبار التصنيفات</button>
                <div id="filtersResult" class="result"></div>
            </div>

            <!-- اختبار الإعدادات -->
            <div class="test-section">
                <h2>⚙️ اختبار الإعدادات</h2>
                <button class="test-button" id="testSettingsBtn">اختبار حفظ الإعدادات</button>
                <button class="test-button" id="testLoadSettingsBtn">اختبار تحميل الإعدادات</button>
                <button class="test-button" id="openOptionsPageBtn">فتح صفحة الإعدادات</button>
                <div id="settingsResult" class="result"></div>
            </div>

            <!-- عرض الوظائف التجريبية -->
            <div class="test-section">
                <h2>📋 عرض الوظائف التجريبية</h2>
                <button class="test-button" id="displaySampleJobsBtn">عرض وظائف تجريبية</button>
                <div id="sampleJobs"></div>
            </div>
        </div>
    </div>

    <script>
        // Test functions
        async function testStorage() {
            const result = document.getElementById('storageResult');
            try {
                const testData = {
                    timestamp: Date.now(),
                    message: 'اختبار التخزين المحلي',
                    jobs: [
                        { id: 1, title: 'وظيفة تجريبية 1' },
                        { id: 2, title: 'وظيفة تجريبية 2' }
                    ]
                };

                await chrome.storage.local.set({ testData: testData });
                result.textContent = '✅ تم حفظ البيانات بنجاح:\n' + JSON.stringify(testData, null, 2);
                result.className = 'result success';
            } catch (error) {
                result.textContent = '❌ خطأ في حفظ البيانات:\n' + error.message;
                result.className = 'result error';
            }
        }

        async function testLoadStorage() {
            const result = document.getElementById('storageResult');
            try {
                const data = await chrome.storage.local.get(['testData']);
                if (data.testData) {
                    result.textContent = '✅ تم تحميل البيانات بنجاح:\n' + JSON.stringify(data.testData, null, 2);
                    result.className = 'result success';
                } else {
                    result.textContent = '⚠️ لا توجد بيانات محفوظة';
                    result.className = 'result';
                }
            } catch (error) {
                result.textContent = '❌ خطأ في تحميل البيانات:\n' + error.message;
                result.className = 'result error';
            }
        }

        async function clearStorage() {
            const result = document.getElementById('storageResult');
            try {
                await chrome.storage.local.clear();
                result.textContent = '✅ تم مسح جميع البيانات';
                result.className = 'result success';
            } catch (error) {
                result.textContent = '❌ خطأ في مسح البيانات:\n' + error.message;
                result.className = 'result error';
            }
        }

        function testFetchJobs() {
            const result = document.getElementById('jobsResult');
            const sampleJobs = [
                {
                    id: 'test_1',
                    title: 'مطور Full Stack - React & Node.js',
                    description: 'مطلوب مطور متمرس لبناء تطبيق ويب حديث...',
                    platform: 'upwork',
                    url: 'https://example.com/job1',
                    date: new Date().toISOString(),
                    price: '$25-50/hr',
                    category: 'web'
                },
                {
                    id: 'test_2',
                    title: 'مصمم UI/UX - تطبيقات جوال',
                    description: 'نحتاج مصمم محترف لتصميم واجهات تطبيق جوال...',
                    platform: 'khamsat',
                    url: 'https://example.com/job2',
                    date: new Date().toISOString(),
                    price: '$200-400',
                    category: 'design'
                }
            ];

            result.textContent = '✅ تم جلب الوظائف التجريبية:\n' + JSON.stringify(sampleJobs, null, 2);
            result.className = 'result success';
        }

        function testUpworkJobs() {
            const result = document.getElementById('jobsResult');
            result.textContent = '🔄 محاولة الاتصال بـ Upwork...\n(ملاحظة: قد يفشل بسبب CORS)';
            result.className = 'result';

            // Simulate API call
            setTimeout(() => {
                result.textContent += '\n⚠️ فشل الاتصال المباشر - استخدام البيانات التجريبية';
            }, 2000);
        }

        function testKhamsatJobs() {
            const result = document.getElementById('jobsResult');
            result.textContent = '🔄 محاولة الاتصال بـ خمسات...\n(ملاحظة: قد يفشل بسبب CORS)';
            result.className = 'result';

            setTimeout(() => {
                result.textContent += '\n⚠️ فشل الاتصال المباشر - استخدام البيانات التجريبية';
            }, 2000);
        }

        function testWuzzufJobs() {
            const result = document.getElementById('jobsResult');
            result.textContent = '🔄 محاولة الاتصال بـ وظف...\n(ملاحظة: قد يفشل بسبب CORS)';
            result.className = 'result';

            setTimeout(() => {
                result.textContent += '\n⚠️ فشل الاتصال المباشر - استخدام البيانات التجريبية';
            }, 2000);
        }

        function testFilters() {
            const result = document.getElementById('filtersResult');
            const jobs = [
                { title: 'React Developer', category: 'web', platform: 'upwork' },
                { title: 'Mobile App Designer', category: 'design', platform: 'khamsat' },
                { title: 'Data Scientist', category: 'data', platform: 'wuzzuf' },
                { title: 'Flutter Developer', category: 'mobile', platform: 'upwork' }
            ];

            const webJobs = jobs.filter(job => job.category === 'web');
            const upworkJobs = jobs.filter(job => job.platform === 'upwork');

            result.textContent = `✅ اختبار الفلاتر:
الوظائف الأصلية: ${jobs.length}
وظائف تطوير الويب: ${webJobs.length}
وظائف Upwork: ${upworkJobs.length}

تفاصيل الفلترة:
${JSON.stringify({ webJobs, upworkJobs }, null, 2)}`;
            result.className = 'result success';
        }

        function testCategories() {
            const result = document.getElementById('filtersResult');
            const categories = ['web', 'mobile', 'data', 'design'];
            const categoryNames = {
                'web': 'تطوير الويب',
                'mobile': 'تطوير التطبيقات',
                'data': 'علوم البيانات',
                'design': 'التصميم'
            };

            result.textContent = '✅ التصنيفات المتاحة:\n' + 
                categories.map(cat => `${cat}: ${categoryNames[cat]}`).join('\n');
            result.className = 'result success';
        }

        async function testSettings() {
            const result = document.getElementById('settingsResult');
            try {
                const testSettings = {
                    maxJobs: 10,
                    autoRefresh: true,
                    notifications: false,
                    preferredCategories: ['web', 'mobile']
                };

                await chrome.storage.local.set({ settings: testSettings });
                result.textContent = '✅ تم حفظ الإعدادات:\n' + JSON.stringify(testSettings, null, 2);
                result.className = 'result success';
            } catch (error) {
                result.textContent = '❌ خطأ في حفظ الإعدادات:\n' + error.message;
                result.className = 'result error';
            }
        }

        async function testLoadSettings() {
            const result = document.getElementById('settingsResult');
            try {
                const data = await chrome.storage.local.get(['settings']);
                if (data.settings) {
                    result.textContent = '✅ تم تحميل الإعدادات:\n' + JSON.stringify(data.settings, null, 2);
                    result.className = 'result success';
                } else {
                    result.textContent = '⚠️ لا توجد إعدادات محفوظة';
                    result.className = 'result';
                }
            } catch (error) {
                result.textContent = '❌ خطأ في تحميل الإعدادات:\n' + error.message;
                result.className = 'result error';
            }
        }

        function openOptionsPage() {
            const result = document.getElementById('settingsResult');
            try {
                chrome.runtime.openOptionsPage();
                result.textContent = '✅ تم فتح صفحة الإعدادات';
                result.className = 'result success';
            } catch (error) {
                result.textContent = '❌ خطأ في فتح صفحة الإعدادات:\n' + error.message;
                result.className = 'result error';
            }
        }

        function displaySampleJobs() {
            const container = document.getElementById('sampleJobs');
            const sampleJobs = [
                {
                    title: 'مطور React.js متقدم',
                    platform: 'upwork',
                    description: 'مطلوب مطور React.js خبير لبناء تطبيق ويب تفاعلي مع Redux وTypeScript',
                    price: '$40-60/hr',
                    date: 'منذ ساعتين'
                },
                {
                    title: 'تصميم تطبيق جوال - UI/UX',
                    platform: 'khamsat',
                    description: 'نحتاج مصمم محترف لتصميم واجهات تطبيق جوال حديث ومبتكر',
                    price: '$300-500',
                    date: 'منذ 4 ساعات'
                },
                {
                    title: 'محلل بيانات - Python & SQL',
                    platform: 'wuzzuf',
                    description: 'وظيفة محلل بيانات في شركة تقنية رائدة مع خبرة في Python وSQL',
                    price: '12000-18000 جنيه',
                    date: 'منذ يوم واحد'
                }
            ];

            container.innerHTML = sampleJobs.map(job => `
                <div class="job-card">
                    <div class="job-platform">${job.platform}</div>
                    <div class="job-title">${job.title}</div>
                    <div style="color: #666; font-size: 12px; margin-bottom: 10px;">${job.description}</div>
                    <div style="display: flex; justify-content: space-between; font-size: 11px;">
                        <span style="color: #27ae60;">${job.date}</span>
                        <span style="color: #e74c3c; font-weight: bold;">${job.price}</span>
                    </div>
                </div>
            `).join('');
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', () => {
            console.log('صفحة الاختبار جاهزة');
            
            // Check if extension is loaded
            if (typeof chrome !== 'undefined' && chrome.storage) {
                console.log('✅ الإضافة متاحة');
            } else {
                console.log('❌ الإضافة غير متاحة');
            }
        });
    </script>
</body>
</html>
