# Tech Jobs Finder - Chrome Extension
## فرص العمل التقنية - إضافة كروم

إضافة Chrome لعرض فرص العمل التقنية **الحقيقية فقط** من مواقع Upwork، خمسات، ووظف.

## 🔥 تحديث مهم: بيانات حقيقية فقط!
تم حذف جميع البيانات التجريبية. الإضافة الآن تعتمد على البيانات الحقيقية 100% من المواقع.

## ✅ الميزات

- ✅ **بيانات حقيقية فقط** - لا توجد بيانات تجريبية
- ✅ جلب الوظائف من APIs حقيقية ومباشرة
- ✅ واجهة عربية جميلة ومتجاوبة
- ✅ فلترة حسب المنصة والتخصص
- ✅ تخزين محلي للبيانات الحقيقية
- ✅ تحديث تلقائي للوظائف
- ✅ فتح الوظائف في تبويب جديد
- ❌ **لا توجد بيانات وهمية أو تجريبية**

## 📁 هيكل المشروع

```
Extension/
├── manifest.json          # ملف التكوين الأساسي
├── popup.html             # واجهة الإضافة
├── popup.css              # تنسيق الواجهة
├── popup.js               # منطق الواجهة
├── background.js          # معالجة CORS والخلفية
├── content.js             # حقن السكريبت في الصفحات
├── icons/                 # أيقونات الإضافة
│   ├── icon16.png
│   ├── icon32.png
│   ├── icon48.png
│   ├── icon128.png
│   └── create_icons.html  # لإنشاء الأيقونات
└── README.md              # هذا الملف
```

## 🚀 طريقة التثبيت

### 1. إنشاء الأيقونات
```bash
# افتح ملف إنشاء الأيقونات في المتصفح
open icons/create_icons.html
# أو
start icons/create_icons.html
```

### 2. تحميل الإضافة في Chrome
1. افتح Chrome واذهب إلى `chrome://extensions/`
2. فعّل "Developer mode" في الزاوية العلوية اليمنى
3. اضغط "Load unpacked" واختر مجلد المشروع
4. ستظهر الإضافة في شريط الأدوات

### 3. الاستخدام
- اضغط على أيقونة الإضافة في شريط الأدوات
- انتظر تحميل فرص العمل
- استخدم الفلاتر لتصفية النتائج
- اضغط على أي وظيفة لفتحها في تبويب جديد

## 🔧 التطوير والتخصيص

### إضافة منصة جديدة
1. أضف المنصة في `manifest.json` تحت `host_permissions`
2. أنشئ دالة جلب البيانات في `background.js`
3. أضف معالج في `content.js` للسكرابينغ
4. حدث الفلاتر في `popup.html` و `popup.js`

### تخصيص التصميم
- عدّل `popup.css` لتغيير الألوان والخطوط
- غيّر أبعاد النافذة في `popup.html`
- أضف رسوم متحركة أو تأثيرات جديدة

### إضافة ميزات جديدة
- تنبيهات للوظائف الجديدة
- حفظ الوظائف المفضلة
- تصدير البيانات
- إحصائيات مفصلة

## 🛠️ حل مشاكل CORS

### الطرق المستخدمة:
1. **Background Script**: للتعامل مع طلبات HTTP
2. **Content Script**: لسكرابة الصفحات المفتوحة
3. **CORS Proxy**: استخدام خدمات وسيطة (محدود)
4. **Mock Data**: بيانات تجريبية للاختبار

### بدائل للبيانات الحقيقية:
```javascript
// استخدام RSS إذا توفر
const rssUrl = 'https://example.com/jobs.rss';

// أو استخدام APIs غير رسمية
const apiUrl = 'https://api.example.com/jobs';

// أو سكرابة الصفحات العامة
const publicUrl = 'https://example.com/jobs/search';
```

## 📊 مثال على البيانات

```javascript
{
  id: 'upwork_123',
  title: 'Full Stack Developer',
  description: 'Looking for experienced developer...',
  platform: 'upwork',
  url: 'https://www.upwork.com/jobs/~123',
  date: '2024-01-15T10:30:00Z',
  price: '$25-50/hr',
  category: 'web'
}
```

## 🔮 أفكار التطوير المستقبلية

### المرحلة الثانية:
- [ ] إضافة المزيد من المنصات (Freelancer، Fiverr)
- [ ] فلترة متقدمة (السعر، التاريخ، المهارات)
- [ ] نظام تنبيهات للوظائف الجديدة
- [ ] حفظ البحثات المفضلة

### المرحلة الثالثة:
- [ ] تحليل اتجاهات السوق
- [ ] توصيات شخصية
- [ ] تكامل مع التقويم
- [ ] تصدير البيانات (CSV، PDF)

### المرحلة الرابعة:
- [ ] ذكاء اصطناعي لتحليل الوظائف
- [ ] مطابقة المهارات مع الوظائف
- [ ] إحصائيات مفصلة
- [ ] واجهة ويب منفصلة

## 🐛 المشاكل المعروفة

1. **CORS**: بعض المواقع تمنع الوصول المباشر
2. **Rate Limiting**: قيود على عدد الطلبات
3. **Dynamic Content**: المحتوى المحمل بـ JavaScript
4. **Anti-Bot**: أنظمة منع البوتات

## 🤝 المساهمة

1. Fork المشروع
2. أنشئ branch جديد (`git checkout -b feature/amazing-feature`)
3. Commit التغييرات (`git commit -m 'Add amazing feature'`)
4. Push إلى Branch (`git push origin feature/amazing-feature`)
5. افتح Pull Request

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - انظر ملف [LICENSE](LICENSE) للتفاصيل.

## 📞 التواصل

- GitHub: [Your GitHub Profile]
- Email: <EMAIL>
- LinkedIn: [Your LinkedIn Profile]

---

**ملاحظة**: هذه الإضافة للأغراض التعليمية والتطويرية. تأكد من احترام شروط الخدمة للمواقع المستهدفة.
