<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار جلب البيانات الحقيقية</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            direction: rtl;
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .content {
            padding: 30px;
        }

        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #eee;
            border-radius: 8px;
            background: #f9f9f9;
        }

        .test-button {
            background: #4facfe;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .test-button:hover {
            background: #357abd;
            transform: translateY(-2px);
        }

        .result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 6px;
            background: #e9ecef;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }

        .success {
            background: #d4edda;
            color: #155724;
        }

        .error {
            background: #f8d7da;
            color: #721c24;
        }

        .job-card {
            background: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .job-title {
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 8px;
        }

        .job-platform {
            display: inline-block;
            background: #4facfe;
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 10px;
            margin-bottom: 8px;
        }

        .loading {
            text-align: center;
            padding: 20px;
        }

        .spinner {
            width: 30px;
            height: 30px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #4facfe;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 اختبار جلب البيانات الحقيقية</h1>
            <p>اختبار طرق مختلفة لجلب بيانات حقيقية من مواقع التوظيف</p>
        </div>

        <div class="content">
            <!-- اختبار Wuzzuf APIs -->
            <div class="test-section">
                <h2>🇪🇬 اختبار Wuzzuf APIs</h2>
                <button class="test-button" onclick="testWuzzufAPI()">اختبار Wuzzuf API</button>
                <button class="test-button" onclick="testWuzzufRSS()">اختبار Wuzzuf RSS</button>
                <button class="test-button" onclick="testWuzzufScraping()">اختبار Wuzzuf Scraping</button>
                <div id="wuzzufResult" class="result"></div>
            </div>

            <!-- اختبار Upwork -->
            <div class="test-section">
                <h2>🌍 اختبار Upwork</h2>
                <button class="test-button" onclick="testUpworkRSS()">اختبار Upwork RSS</button>
                <button class="test-button" onclick="testUpworkProxy()">اختبار Upwork Proxy</button>
                <button class="test-button" onclick="testUpworkAlternative()">اختبار Upwork Alternative</button>
                <div id="upworkResult" class="result"></div>
            </div>

            <!-- اختبار Khamsat -->
            <div class="test-section">
                <h2>🇸🇦 اختبار Khamsat</h2>
                <button class="test-button" onclick="testKhamsatScraping()">اختبار Khamsat Scraping</button>
                <button class="test-button" onclick="testKhamsatProxy()">اختبار Khamsat Proxy</button>
                <div id="khamsatResult" class="result"></div>
            </div>

            <!-- اختبار جميع الطرق -->
            <div class="test-section">
                <h2>🚀 اختبار شامل</h2>
                <button class="test-button" onclick="testAllMethods()">اختبار جميع الطرق</button>
                <div id="allResult" class="result"></div>
            </div>

            <!-- عرض النتائج -->
            <div class="test-section">
                <h2>📋 النتائج النهائية</h2>
                <div id="finalResults"></div>
            </div>
        </div>
    </div>

    <script>
        // Test Wuzzuf API endpoints
        async function testWuzzufAPI() {
            const result = document.getElementById('wuzzufResult');
            result.innerHTML = '<div class="loading"><div class="spinner"></div>جاري اختبار Wuzzuf API...</div>';
            
            const apiEndpoints = [
                'https://wuzzuf.net/api/jobs?q=developer',
                'https://wuzzuf.net/search/jobs/api?q=programmer',
                'https://api.wuzzuf.net/jobs/search?query=software'
            ];
            
            let successCount = 0;
            let results = [];
            
            for (const endpoint of apiEndpoints) {
                try {
                    console.log(`Testing: ${endpoint}`);
                    const response = await fetch(endpoint, {
                        headers: {
                            'Accept': 'application/json',
                            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                        }
                    });
                    
                    if (response.ok) {
                        const data = await response.json();
                        results.push(`✅ ${endpoint}: نجح (${JSON.stringify(data).length} bytes)`);
                        successCount++;
                    } else {
                        results.push(`❌ ${endpoint}: فشل (${response.status})`);
                    }
                } catch (error) {
                    results.push(`❌ ${endpoint}: خطأ - ${error.message}`);
                }
            }
            
            result.textContent = `نتائج اختبار Wuzzuf API:\n${results.join('\n')}\n\nنجح: ${successCount}/${apiEndpoints.length}`;
            result.className = successCount > 0 ? 'result success' : 'result error';
        }

        // Test Wuzzuf RSS
        async function testWuzzufRSS() {
            const result = document.getElementById('wuzzufResult');
            result.innerHTML = '<div class="loading"><div class="spinner"></div>جاري اختبار Wuzzuf RSS...</div>';
            
            try {
                const rssUrl = 'https://wuzzuf.net/search/jobs/rss?q=developer';
                const response = await fetch(`https://api.rss2json.com/v1/api.json?rss_url=${encodeURIComponent(rssUrl)}`);
                
                if (response.ok) {
                    const data = await response.json();
                    if (data.items && data.items.length > 0) {
                        result.textContent = `✅ نجح RSS Wuzzuf!\nعدد الوظائف: ${data.items.length}\nأول وظيفة: ${data.items[0].title}`;
                        result.className = 'result success';
                    } else {
                        result.textContent = '⚠️ RSS يعمل لكن لا توجد وظائف';
                        result.className = 'result';
                    }
                } else {
                    result.textContent = `❌ فشل RSS: ${response.status}`;
                    result.className = 'result error';
                }
            } catch (error) {
                result.textContent = `❌ خطأ في RSS: ${error.message}`;
                result.className = 'result error';
            }
        }

        // Test Wuzzuf scraping with proxy
        async function testWuzzufScraping() {
            const result = document.getElementById('wuzzufResult');
            result.innerHTML = '<div class="loading"><div class="spinner"></div>جاري اختبار Wuzzuf Scraping...</div>';
            
            const proxies = [
                'https://api.allorigins.win/get?url=',
                'https://thingproxy.freeboard.io/fetch/',
                'https://yacdn.org/proxy/'
            ];
            
            for (const proxy of proxies) {
                try {
                    const targetUrl = 'https://wuzzuf.net/search/jobs/?q=developer';
                    const response = await fetch(proxy + encodeURIComponent(targetUrl));
                    
                    if (response.ok) {
                        const data = await response.text();
                        let html = data;
                        
                        if (proxy.includes('allorigins')) {
                            const jsonData = JSON.parse(data);
                            html = jsonData.contents;
                        }
                        
                        if (html.includes('wuzzuf') || html.includes('job')) {
                            result.textContent = `✅ نجح Scraping مع ${proxy}!\nحجم البيانات: ${html.length} حرف`;
                            result.className = 'result success';
                            return;
                        }
                    }
                } catch (error) {
                    console.log(`Proxy ${proxy} failed:`, error.message);
                }
            }
            
            result.textContent = '❌ فشل جميع proxy services';
            result.className = 'result error';
        }

        // Test Upwork RSS
        async function testUpworkRSS() {
            const result = document.getElementById('upworkResult');
            result.innerHTML = '<div class="loading"><div class="spinner"></div>جاري اختبار Upwork RSS...</div>';
            
            try {
                const rssUrl = 'https://www.upwork.com/ab/feed/jobs/rss?q=web%20development&sort=recency';
                const response = await fetch(`https://api.rss2json.com/v1/api.json?rss_url=${encodeURIComponent(rssUrl)}`);
                
                if (response.ok) {
                    const data = await response.json();
                    if (data.items && data.items.length > 0) {
                        result.textContent = `✅ نجح RSS Upwork!\nعدد الوظائف: ${data.items.length}\nأول وظيفة: ${data.items[0].title}`;
                        result.className = 'result success';
                    } else {
                        result.textContent = '⚠️ RSS يعمل لكن لا توجد وظائف';
                        result.className = 'result';
                    }
                } else {
                    result.textContent = `❌ فشل RSS: ${response.status}`;
                    result.className = 'result error';
                }
            } catch (error) {
                result.textContent = `❌ خطأ في RSS: ${error.message}`;
                result.className = 'result error';
            }
        }

        // Test all methods
        async function testAllMethods() {
            const result = document.getElementById('allResult');
            result.innerHTML = '<div class="loading"><div class="spinner"></div>جاري اختبار جميع الطرق...</div>';
            
            const tests = [
                { name: 'Wuzzuf API', func: testWuzzufAPI },
                { name: 'Wuzzuf RSS', func: testWuzzufRSS },
                { name: 'Upwork RSS', func: testUpworkRSS }
            ];
            
            let results = [];
            
            for (const test of tests) {
                try {
                    await test.func();
                    results.push(`✅ ${test.name}: نجح`);
                } catch (error) {
                    results.push(`❌ ${test.name}: فشل - ${error.message}`);
                }
            }
            
            result.textContent = `نتائج الاختبار الشامل:\n${results.join('\n')}`;
            result.className = 'result';
        }

        // Placeholder functions
        function testUpworkProxy() {
            document.getElementById('upworkResult').textContent = 'جاري التطوير...';
        }

        function testUpworkAlternative() {
            document.getElementById('upworkResult').textContent = 'جاري التطوير...';
        }

        function testKhamsatScraping() {
            document.getElementById('khamsatResult').textContent = 'جاري التطوير...';
        }

        function testKhamsatProxy() {
            document.getElementById('khamsatResult').textContent = 'جاري التطوير...';
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', () => {
            console.log('صفحة اختبار البيانات الحقيقية جاهزة');
        });
    </script>
</body>
</html>
