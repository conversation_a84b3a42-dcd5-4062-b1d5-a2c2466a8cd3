<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فرص العمل التقنية</title>
    <link rel="stylesheet" href="popup.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <div class="container">
        <header class="header">
            <div class="header-content">
                <h1><i class="fas fa-rocket"></i> فرص العمل التقنية</h1>
                <p class="header-subtitle">اكتشف أحدث الفرص التقنية</p>
            </div>
            <button class="refresh-btn" id="refreshBtn">
                <i class="fas fa-sync-alt"></i>
                <span>تحديث</span>
            </button>
        </header>

        <div class="filters">
            <div class="select-wrapper">
                <i class="fas fa-globe"></i>
                <select id="platformFilter">
                    <option value="all">جميع المنصات</option>
                    <option value="upwork">Upwork</option>
                    <option value="khamsat">خمسات</option>
                    <option value="wuzzuf">وظف</option>
                </select>
            </div>
            
            <div class="select-wrapper">
                <i class="fas fa-code"></i>
                <select id="categoryFilter">
                    <option value="all">جميع التخصصات</option>
                    <option value="web">تطوير الويب</option>
                    <option value="mobile">تطوير التطبيقات</option>
                    <option value="data">علوم البيانات</option>
                    <option value="design">التصميم</option>
                </select>
            </div>
        </div>

        <div class="loading" id="loading">
            <div class="spinner"></div>
            <p>جاري تحميل فرص العمل...</p>
        </div>

        <div class="error" id="error" style="display: none;">
            <p>❌ حدث خطأ في تحميل البيانات</p>
            <button id="retryBtn">إعادة المحاولة</button>
        </div>

        <div class="jobs-container" id="jobsContainer">
            <!-- سيتم ملء الوظائف هنا بواسطة JavaScript -->
        </div>

        <div class="stats" id="stats">
            <p>آخر تحديث: <span id="lastUpdate">لم يتم التحديث بعد</span></p>
            <p>إجمالي الوظائف: <span id="totalJobs">0</span></p>
        </div>

        <footer class="footer">
            <p>تم تطويره بواسطة Chrome Extension</p>
            <div class="social-links">
                <a href="#" title="إعدادات">⚙️</a>
                <a href="#" title="معلومات">ℹ️</a>
            </div>
        </footer>
    </div>

    <script src="popup.js"></script>
</body>
</html>
