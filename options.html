<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعدادات فرص العمل التقنية</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            direction: rtl;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 28px;
            margin-bottom: 10px;
        }

        .content {
            padding: 30px;
        }

        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #eee;
            border-radius: 8px;
            background: #f9f9f9;
        }

        .section h2 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 20px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #555;
        }

        .form-group input,
        .form-group select {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
            direction: rtl;
        }

        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 10px;
        }

        .checkbox-group input[type="checkbox"] {
            width: auto;
        }

        .button-group {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-top: 30px;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: bold;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: #4facfe;
            color: white;
        }

        .btn-primary:hover {
            background: #357abd;
            transform: translateY(-2px);
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background: #545b62;
        }

        .status {
            padding: 15px;
            border-radius: 6px;
            margin-top: 20px;
            text-align: center;
            display: none;
        }

        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>⚙️ إعدادات فرص العمل التقنية</h1>
            <p>خصص تجربتك في البحث عن الوظائف</p>
        </div>

        <div class="content">
            <form id="settingsForm">
                <!-- إعدادات عامة -->
                <div class="section">
                    <h2>🔧 الإعدادات العامة</h2>
                    
                    <div class="form-group">
                        <label for="maxJobs">الحد الأقصى للوظائف المعروضة:</label>
                        <select id="maxJobs">
                            <option value="5">5 وظائف</option>
                            <option value="10">10 وظائف</option>
                            <option value="15">15 وظيفة</option>
                            <option value="20">20 وظيفة</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="refreshInterval">فترة التحديث التلقائي (بالدقائق):</label>
                        <select id="refreshInterval">
                            <option value="15">15 دقيقة</option>
                            <option value="30">30 دقيقة</option>
                            <option value="60">ساعة واحدة</option>
                            <option value="120">ساعتان</option>
                            <option value="0">إيقاف التحديث التلقائي</option>
                        </select>
                    </div>

                    <div class="checkbox-group">
                        <input type="checkbox" id="autoRefresh">
                        <label for="autoRefresh">تفعيل التحديث التلقائي</label>
                    </div>

                    <div class="checkbox-group">
                        <input type="checkbox" id="notifications">
                        <label for="notifications">إرسال تنبيهات للوظائف الجديدة</label>
                    </div>
                </div>

                <!-- إعدادات المنصات -->
                <div class="section">
                    <h2>🌐 إعدادات المنصات</h2>
                    
                    <div class="checkbox-group">
                        <input type="checkbox" id="enableUpwork" checked>
                        <label for="enableUpwork">تفعيل Upwork</label>
                    </div>

                    <div class="checkbox-group">
                        <input type="checkbox" id="enableKhamsat" checked>
                        <label for="enableKhamsat">تفعيل خمسات</label>
                    </div>

                    <div class="checkbox-group">
                        <input type="checkbox" id="enableWuzzuf" checked>
                        <label for="enableWuzzuf">تفعيل وظف</label>
                    </div>
                </div>

                <!-- فلاتر البحث -->
                <div class="section">
                    <h2>🔍 فلاتر البحث المفضلة</h2>
                    
                    <div class="form-group">
                        <label for="preferredCategories">التخصصات المفضلة:</label>
                        <div class="checkbox-group">
                            <input type="checkbox" id="catWeb" value="web">
                            <label for="catWeb">تطوير الويب</label>
                        </div>
                        <div class="checkbox-group">
                            <input type="checkbox" id="catMobile" value="mobile">
                            <label for="catMobile">تطوير التطبيقات</label>
                        </div>
                        <div class="checkbox-group">
                            <input type="checkbox" id="catData" value="data">
                            <label for="catData">علوم البيانات</label>
                        </div>
                        <div class="checkbox-group">
                            <input type="checkbox" id="catDesign" value="design">
                            <label for="catDesign">التصميم</label>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="keywords">كلمات مفتاحية للبحث (مفصولة بفاصلة):</label>
                        <input type="text" id="keywords" placeholder="React, Node.js, Python, تطوير ويب">
                    </div>

                    <div class="form-group">
                        <label for="excludeKeywords">كلمات مفتاحية للاستبعاد:</label>
                        <input type="text" id="excludeKeywords" placeholder="WordPress, PHP">
                    </div>
                </div>

                <!-- إعدادات متقدمة -->
                <div class="section">
                    <h2>⚡ إعدادات متقدمة</h2>
                    
                    <div class="checkbox-group">
                        <input type="checkbox" id="enableCache">
                        <label for="enableCache">تفعيل التخزين المؤقت</label>
                    </div>

                    <div class="form-group">
                        <label for="cacheExpiry">مدة انتهاء صلاحية التخزين المؤقت (بالساعات):</label>
                        <select id="cacheExpiry">
                            <option value="1">ساعة واحدة</option>
                            <option value="6">6 ساعات</option>
                            <option value="12">12 ساعة</option>
                            <option value="24">24 ساعة</option>
                        </select>
                    </div>

                    <div class="checkbox-group">
                        <input type="checkbox" id="debugMode">
                        <label for="debugMode">تفعيل وضع التطوير (Debug Mode)</label>
                    </div>
                </div>

                <div class="button-group">
                    <button type="submit" class="btn btn-primary">💾 حفظ الإعدادات</button>
                    <button type="button" id="resetBtn" class="btn btn-secondary">🔄 إعادة تعيين</button>
                    <button type="button" id="exportBtn" class="btn btn-secondary">📤 تصدير الإعدادات</button>
                </div>

                <div id="status" class="status"></div>
            </form>
        </div>
    </div>

    <script src="options.js"></script>
</body>
</html>
