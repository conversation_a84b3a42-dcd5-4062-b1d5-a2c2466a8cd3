#!/usr/bin/env python3
"""
Simple script to create basic PNG icons for the Chrome extension
"""

from PIL import Image, ImageDraw, ImageFont
import os

def create_icon(size, filename):
    """Create a simple icon with gradient background and rocket emoji"""
    
    # Create image with transparent background
    img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    # Create gradient background (blue to cyan)
    for y in range(size):
        # Calculate color based on position
        ratio = y / size
        r = int(79 + (0 - 79) * ratio)      # 79 -> 0
        g = int(172 + (242 - 172) * ratio)  # 172 -> 242
        b = int(254 + (254 - 254) * ratio)  # 254 -> 254
        
        # Draw horizontal line
        draw.line([(0, y), (size, y)], fill=(r, g, b, 255))
    
    # Add rounded corners
    mask = Image.new('L', (size, size), 0)
    mask_draw = ImageDraw.Draw(mask)
    corner_radius = size // 8
    mask_draw.rounded_rectangle([0, 0, size, size], corner_radius, fill=255)
    
    # Apply mask
    img.putalpha(mask)
    
    # Add rocket symbol (simple white triangle and rectangle)
    if size >= 32:
        # Rocket body (rectangle)
        body_width = size // 4
        body_height = size // 2
        body_x = (size - body_width) // 2
        body_y = (size - body_height) // 2 + size // 8
        
        draw.rectangle([body_x, body_y, body_x + body_width, body_y + body_height], 
                      fill=(255, 255, 255, 255))
        
        # Rocket tip (triangle)
        tip_size = size // 6
        tip_x = size // 2
        tip_y = body_y - tip_size
        
        draw.polygon([(tip_x, tip_y), 
                     (tip_x - tip_size//2, body_y), 
                     (tip_x + tip_size//2, body_y)], 
                    fill=(255, 255, 255, 255))
        
        # Rocket flames (small triangles)
        flame_size = size // 8
        flame_y = body_y + body_height
        
        # Left flame
        draw.polygon([(body_x + body_width//4, flame_y),
                     (body_x, flame_y + flame_size),
                     (body_x + body_width//2, flame_y + flame_size//2)],
                    fill=(255, 165, 0, 255))  # Orange
        
        # Right flame
        draw.polygon([(body_x + 3*body_width//4, flame_y),
                     (body_x + body_width, flame_y + flame_size),
                     (body_x + body_width//2, flame_y + flame_size//2)],
                    fill=(255, 165, 0, 255))  # Orange
    
    # Save the image
    img.save(filename, 'PNG')
    print(f"Created {filename} ({size}x{size})")

def main():
    """Create all required icon sizes"""
    
    # Create icons directory if it doesn't exist
    if not os.path.exists('icons'):
        os.makedirs('icons')
    
    # Icon sizes required by Chrome extensions
    sizes = [16, 32, 48, 128]
    
    for size in sizes:
        filename = f'icons/icon{size}.png'
        create_icon(size, filename)
    
    print("\n✅ All icons created successfully!")
    print("Icons are saved in the 'icons' directory")
    print("\nTo use these icons:")
    print("1. Make sure the 'icons' directory is in your extension folder")
    print("2. The manifest.json already references these files")
    print("3. Load the extension in Chrome")

if __name__ == "__main__":
    try:
        main()
    except ImportError:
        print("❌ PIL (Pillow) is required to create icons")
        print("Install it with: pip install Pillow")
        print("\nAlternatively, you can:")
        print("1. Open icons/create_icons.html in your browser")
        print("2. Or create icons manually using any image editor")
        print("3. Save them as icon16.png, icon32.png, icon48.png, icon128.png in the icons folder")
    except Exception as e:
        print(f"❌ Error creating icons: {e}")
        print("\nYou can create icons manually:")
        print("1. Create 16x16, 32x32, 48x48, and 128x128 PNG images")
        print("2. Save them in the icons folder with the correct names")
        print("3. Use any image editor or online icon generator")
